<?php

// Script pour créer la table logs
echo "=== Création de la table logs ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

try {
    echo "1. Vérification de la connexion...\n";
    DB::connection()->getPdo();
    echo "✓ Connexion réussie\n";

    echo "\n2. Vérification de la table logs...\n";
    
    if (Schema::hasTable('logs')) {
        echo "✓ Table logs existe déjà\n";
        
        // Vérifier la structure
        $columns = Schema::getColumnListing('logs');
        echo "Colonnes: " . implode(', ', $columns) . "\n";
        
    } else {
        echo "Table logs manquante. Création...\n";
        
        Schema::create('logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->text('details')->nullable();
            $table->timestamp('created_at')->nullable();

            $table->index('user_id');
            $table->index('action');
        });
        
        echo "✓ Table logs créée avec succès\n";
    }

    echo "\n3. Test d'insertion...\n";
    
    // Vérifier qu'il y a au moins un utilisateur
    $user = DB::table('users')->first();
    if (!$user) {
        echo "❌ Aucun utilisateur trouvé. Création d'un utilisateur de test...\n";
        
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'admin',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        $user = DB::table('users')->where('email', '<EMAIL>')->first();
        echo "✓ Utilisateur de test créé\n";
    }
    
    // Test d'insertion dans logs
    $logId = DB::table('logs')->insertGetId([
        'user_id' => $user->id,
        'action' => 'test_creation',
        'details' => 'Test de création de la table logs',
        'created_at' => now()
    ]);
    
    echo "✓ Log de test créé avec ID: $logId\n";
    
    // Nettoyer le test
    DB::table('logs')->where('id', $logId)->delete();
    echo "✓ Log de test supprimé\n";

    echo "\n=== Table logs prête à l'utilisation ===\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
    
    if (strpos($e->getMessage(), 'Base table or view not found') !== false) {
        echo "\nTentative de création manuelle...\n";
        try {
            DB::statement("
                CREATE TABLE IF NOT EXISTS logs (
                    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    user_id BIGINT UNSIGNED NOT NULL,
                    action VARCHAR(191) NOT NULL,
                    details TEXT NULL,
                    created_at TIMESTAMP NULL,
                    INDEX idx_logs_user_id (user_id),
                    INDEX idx_logs_action (action),
                    CONSTRAINT fk_logs_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Table logs créée manuellement\n";
        } catch (Exception $e2) {
            echo "❌ Échec de la création manuelle: " . $e2->getMessage() . "\n";
        }
    }
}
