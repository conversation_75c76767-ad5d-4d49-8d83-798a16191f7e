<?php

// Script d'optimisation des performances ClockIn
echo "=== Optimisation des performances ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

$optimizations = [];

function logOptimization($message) {
    global $optimizations;
    $optimizations[] = $message;
    echo "⚡ $message\n";
}

try {
    echo "1. Optimisation des index de base de données...\n";
    
    // Index pour la table logs
    if (Schema::hasTable('logs')) {
        $indexes = DB::select("SHOW INDEX FROM logs WHERE Key_name != 'PRIMARY'");
        $existingIndexes = array_column($indexes, 'Key_name');
        
        if (!in_array('idx_logs_user_action', $existingIndexes)) {
            DB::statement("CREATE INDEX idx_logs_user_action ON logs(user_id, action)");
            logOptimization("Index composite créé sur logs(user_id, action)");
        }
        
        if (!in_array('idx_logs_created_at', $existingIndexes)) {
            DB::statement("CREATE INDEX idx_logs_created_at ON logs(created_at)");
            logOptimization("Index créé sur logs(created_at)");
        }
    }
    
    // Index pour la table pointages
    if (Schema::hasTable('pointages')) {
        $indexes = DB::select("SHOW INDEX FROM pointages WHERE Key_name != 'PRIMARY'");
        $existingIndexes = array_column($indexes, 'Key_name');
        
        if (!in_array('idx_pointages_user_date', $existingIndexes)) {
            DB::statement("CREATE INDEX idx_pointages_user_date ON pointages(user_id, debut_pointage)");
            logOptimization("Index composite créé sur pointages(user_id, debut_pointage)");
        }
        
        if (!in_array('idx_pointages_site_date', $existingIndexes)) {
            DB::statement("CREATE INDEX idx_pointages_site_date ON pointages(site_id, debut_pointage)");
            logOptimization("Index composite créé sur pointages(site_id, debut_pointage)");
        }
    }
    
    // Index pour la table verifications
    if (Schema::hasTable('verifications')) {
        $indexes = DB::select("SHOW INDEX FROM verifications WHERE Key_name != 'PRIMARY'");
        $existingIndexes = array_column($indexes, 'Key_name');
        
        if (!in_array('idx_verifications_user_date', $existingIndexes)) {
            DB::statement("CREATE INDEX idx_verifications_user_date ON verifications(user_id, date_heure)");
            logOptimization("Index composite créé sur verifications(user_id, date_heure)");
        }
    }

    echo "\n2. Optimisation des tables...\n";
    
    // Analyser et optimiser les tables
    $tables = ['users', 'sites', 'pointages', 'verifications', 'assignments', 'logs'];
    
    foreach ($tables as $table) {
        if (Schema::hasTable($table)) {
            DB::statement("ANALYZE TABLE $table");
            DB::statement("OPTIMIZE TABLE $table");
            logOptimization("Table $table analysée et optimisée");
        }
    }

    echo "\n3. Configuration du cache de requêtes...\n";
    
    // Vérifier la configuration du cache
    $cacheConfig = config('cache.default');
    if ($cacheConfig === 'database') {
        // Créer la table cache si elle n'existe pas
        if (!Schema::hasTable('cache')) {
            DB::statement("
                CREATE TABLE cache (
                    `key` varchar(255) NOT NULL,
                    `value` mediumtext NOT NULL,
                    `expiration` int NOT NULL,
                    PRIMARY KEY (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            logOptimization("Table cache créée");
        }
        
        if (!Schema::hasTable('cache_locks')) {
            DB::statement("
                CREATE TABLE cache_locks (
                    `key` varchar(255) NOT NULL,
                    `owner` varchar(255) NOT NULL,
                    `expiration` int NOT NULL,
                    PRIMARY KEY (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            logOptimization("Table cache_locks créée");
        }
    }

    echo "\n4. Optimisation des requêtes fréquentes...\n";
    
    // Créer des vues pour les requêtes complexes
    $viewExists = DB::select("SHOW TABLES LIKE 'view_user_pointages_summary'");
    if (empty($viewExists)) {
        DB::statement("
            CREATE VIEW view_user_pointages_summary AS
            SELECT 
                u.id as user_id,
                u.name as user_name,
                u.email,
                COUNT(p.id) as total_pointages,
                MAX(p.debut_pointage) as last_pointage,
                AVG(TIME_TO_SEC(p.duree)) as avg_duration_seconds
            FROM users u
            LEFT JOIN pointages p ON u.id = p.user_id
            GROUP BY u.id, u.name, u.email
        ");
        logOptimization("Vue view_user_pointages_summary créée");
    }

    echo "\n5. Configuration des paramètres MySQL...\n";
    
    // Optimisations MySQL recommandées
    $mysqlOptimizations = [
        "SET GLOBAL innodb_buffer_pool_size = 128M",
        "SET GLOBAL query_cache_size = 32M",
        "SET GLOBAL query_cache_type = 1",
        "SET GLOBAL tmp_table_size = 32M",
        "SET GLOBAL max_heap_table_size = 32M"
    ];
    
    foreach ($mysqlOptimizations as $optimization) {
        try {
            DB::statement($optimization);
            logOptimization("Configuration MySQL appliquée: " . explode(' = ', $optimization)[0]);
        } catch (Exception $e) {
            echo "⚠️  Impossible d'appliquer: $optimization (permissions requises)\n";
        }
    }

    echo "\n6. Nettoyage et maintenance...\n";
    
    // Nettoyer les logs anciens (plus de 30 jours)
    $oldLogsCount = DB::table('logs')
        ->where('created_at', '<', now()->subDays(30))
        ->count();
    
    if ($oldLogsCount > 0) {
        DB::table('logs')
            ->where('created_at', '<', now()->subDays(30))
            ->delete();
        logOptimization("$oldLogsCount anciens logs supprimés");
    }
    
    // Nettoyer les sessions expirées si la table existe
    if (Schema::hasTable('sessions')) {
        $expiredSessions = DB::table('sessions')
            ->where('last_activity', '<', time() - 86400)
            ->count();
        
        if ($expiredSessions > 0) {
            DB::table('sessions')
                ->where('last_activity', '<', time() - 86400)
                ->delete();
            logOptimization("$expiredSessions sessions expirées supprimées");
        }
    }

    echo "\n7. Statistiques de performance...\n";
    
    // Afficher quelques statistiques
    $stats = [
        'Utilisateurs' => DB::table('users')->count(),
        'Sites' => DB::table('sites')->count(),
        'Pointages' => DB::table('pointages')->count(),
        'Logs' => DB::table('logs')->count(),
        'Vérifications' => DB::table('verifications')->count()
    ];
    
    foreach ($stats as $table => $count) {
        echo "  📊 $table: $count enregistrements\n";
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "OPTIMISATION TERMINÉE\n";
    echo str_repeat("=", 60) . "\n";
    
    if (!empty($optimizations)) {
        echo "\n⚡ OPTIMISATIONS APPLIQUÉES:\n";
        foreach ($optimizations as $opt) {
            echo "  • $opt\n";
        }
    }
    
    echo "\n🚀 RECOMMANDATIONS SUPPLÉMENTAIRES:\n";
    echo "  • Configurer un cache Redis pour de meilleures performances\n";
    echo "  • Mettre en place une stratégie de sauvegarde automatique\n";
    echo "  • Surveiller les performances avec des outils de monitoring\n";
    echo "  • Configurer la compression gzip sur le serveur web\n";
    echo "  • Utiliser un CDN pour les assets statiques\n";

} catch (Exception $e) {
    echo "❌ Erreur lors de l'optimisation: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
