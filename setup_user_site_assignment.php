<?php

// Script pour assigner l'utilisateur admin à un site
echo "=== Configuration de l'assignation utilisateur-site ===\n";

// Charger <PERSON>vel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "1. Vérification de la connexion...\n";
    DB::connection()->getPdo();
    echo "✅ Connexion réussie\n";

    echo "\n2. Vérification des utilisateurs...\n";
    $users = DB::table('users')->get();
    
    foreach ($users as $user) {
        echo "  - ID: {$user->id}, Name: {$user->name}, Email: {$user->email}, Role: {$user->role}\n";
    }

    echo "\n3. Vérification des sites...\n";
    $sites = DB::table('sites')->get();
    
    if ($sites->count() === 0) {
        echo "Aucun site trouvé. Création de sites de test...\n";
        
        $siteIds = [];
        
        // Site principal (Casablanca)
        $siteIds[] = DB::table('sites')->insertGetId([
            'name' => 'Site Principal Casablanca',
            'latitude' => 33.5731,
            'longitude' => -7.5898,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Site secondaire
        $siteIds[] = DB::table('sites')->insertGetId([
            'name' => 'Site Secondaire Rabat',
            'latitude' => 34.0209,
            'longitude' => -6.8416,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Site de test
        $siteIds[] = DB::table('sites')->insertGetId([
            'name' => 'Site Test Mohammedia',
            'latitude' => 33.6866,
            'longitude' => -7.3674,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "✅ 3 sites créés\n";
        
        // Recharger les sites
        $sites = DB::table('sites')->get();
    }
    
    foreach ($sites as $site) {
        echo "  - ID: {$site->id}, Name: {$site->name}, Lat: {$site->latitude}, Lon: {$site->longitude}\n";
    }

    echo "\n4. Vérification des assignations existantes...\n";
    $assignments = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->select('assignments.*', 'users.name as user_name', 'sites.name as site_name')
        ->get();
    
    if ($assignments->count() > 0) {
        echo "Assignations existantes:\n";
        foreach ($assignments as $assignment) {
            echo "  - {$assignment->user_name} → {$assignment->site_name}\n";
        }
    } else {
        echo "Aucune assignation trouvée.\n";
    }

    echo "\n5. Assignation de l'utilisateur admin au site principal...\n";
    
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
    $principalSite = DB::table('sites')->first(); // Premier site
    
    if (!$adminUser) {
        echo "❌ Utilisateur admin non trouvé\n";
        return;
    }
    
    if (!$principalSite) {
        echo "❌ Aucun site disponible\n";
        return;
    }
    
    // Vérifier si l'assignation existe déjà
    $existingAssignment = DB::table('assignments')
        ->where('user_id', $adminUser->id)
        ->where('site_id', $principalSite->id)
        ->first();
    
    if (!$existingAssignment) {
        DB::table('assignments')->insert([
            'user_id' => $adminUser->id,
            'site_id' => $principalSite->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✅ Admin assigné au site: {$principalSite->name}\n";
    } else {
        echo "✅ Admin déjà assigné au site: {$principalSite->name}\n";
    }

    echo "\n6. Assignation des autres utilisateurs...\n";
    
    $employees = DB::table('users')->where('role', 'employee')->get();
    $availableSites = DB::table('sites')->get();
    
    foreach ($employees as $index => $employee) {
        $siteIndex = $index % $availableSites->count();
        $site = $availableSites[$siteIndex];
        
        $existingAssignment = DB::table('assignments')
            ->where('user_id', $employee->id)
            ->first();
        
        if (!$existingAssignment) {
            DB::table('assignments')->insert([
                'user_id' => $employee->id,
                'site_id' => $site->id,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            echo "✅ {$employee->name} assigné au site: {$site->name}\n";
        } else {
            $assignedSite = DB::table('sites')->where('id', $existingAssignment->site_id)->first();
            echo "✅ {$employee->name} déjà assigné au site: {$assignedSite->name}\n";
        }
    }

    echo "\n7. Résumé final des assignations...\n";
    
    $finalAssignments = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->select(
            'users.name as user_name',
            'users.email as user_email',
            'users.role as user_role',
            'sites.name as site_name',
            'sites.latitude',
            'sites.longitude'
        )
        ->get();
    
    echo "Assignations finales:\n";
    foreach ($finalAssignments as $assignment) {
        echo sprintf(
            "  - %-20s (%-10s) → %-25s [%.4f, %.4f]\n",
            $assignment->user_name,
            $assignment->user_role,
            $assignment->site_name,
            $assignment->latitude,
            $assignment->longitude
        );
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "CONFIGURATION TERMINÉE\n";
    echo str_repeat("=", 60) . "\n";
    
    echo "\n🎯 PRÊT POUR LES TESTS!\n";
    echo "\nPour tester l'endpoint check-location:\n";
    echo "1. Démarrer le serveur: php artisan serve --host=127.0.0.1 --port=8000\n";
    echo "2. Exécuter le test: php test_check_location.php\n";
    echo "3. Ou utiliser Postman avec le guide: POSTMAN_CHECK_LOCATION_GUIDE.md\n";
    
    echo "\n📍 COORDONNÉES DE TEST POUR ADMIN:\n";
    echo "Site assigné: {$principalSite->name}\n";
    echo "Latitude: {$principalSite->latitude}\n";
    echo "Longitude: {$principalSite->longitude}\n";
    echo "\nCoordonnées proches (dans la zone):\n";
    echo "Latitude: " . ($principalSite->latitude + 0.0001) . "\n";
    echo "Longitude: " . ($principalSite->longitude + 0.0001) . "\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
