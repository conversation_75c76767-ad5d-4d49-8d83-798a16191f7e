<?php

namespace App\Services;

use App\Models\User;
use App\Models\Pointage;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\DetailedPointagesExport;
use App\Exports\EmployeeReportExport;
use App\Exports\SiteReportExport;

/**
 * Service de gestion des exports pour le système ClockIn
 */
class ExportService
{
    /**
     * Génère un rapport Excel détaillé pour tous les employés
     */
    public function generateEmployeeReport(Carbon $startDate, Carbon $endDate, array $options = []): string
    {
        $data = $this->prepareEmployeeReportData($startDate, $endDate, $options);
        
        $filename = 'rapport_employes_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';
        
        Excel::store(new EmployeeReportExport($data), $filename, 'local');
        
        return storage_path('app/' . $filename);
    }

    /**
     * Génère un rapport Excel pour un employé spécifique
     */
    public function generateIndividualEmployeeReport(int $userId, Carbon $startDate, Carbon $endDate): string
    {
        $user = User::findOrFail($userId);
        $data = $this->prepareIndividualEmployeeData($user, $startDate, $endDate);
        
        $filename = 'rapport_' . str_replace(' ', '_', $user->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';
        
        Excel::store(new DetailedPointagesExport($data), $filename, 'local');
        
        return storage_path('app/' . $filename);
    }

    /**
     * Génère un rapport Excel par site
     */
    public function generateSiteReport(int $siteId, Carbon $startDate, Carbon $endDate): string
    {
        $site = Site::findOrFail($siteId);
        $data = $this->prepareSiteReportData($site, $startDate, $endDate);
        
        $filename = 'rapport_site_' . str_replace(' ', '_', $site->name) . '_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '.xlsx';
        
        Excel::store(new SiteReportExport($data), $filename, 'local');
        
        return storage_path('app/' . $filename);
    }

    /**
     * Prépare les données pour le rapport global des employés
     */
    private function prepareEmployeeReportData(Carbon $startDate, Carbon $endDate, array $options): array
    {
        // Récupérer tous les pointages dans la période
        $pointages = Pointage::with(['user', 'site'])
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Grouper par employé
        $employeeData = [];
        
        foreach ($pointages as $pointage) {
            $userId = $pointage->user_id;
            
            if (!isset($employeeData[$userId])) {
                $employeeData[$userId] = [
                    'user' => $pointage->user,
                    'pointages' => [],
                    'statistics' => [
                        'total_pointages' => 0,
                        'completed_pointages' => 0,
                        'total_hours' => 0,
                        'average_daily_hours' => 0,
                        'sites_worked' => []
                    ]
                ];
            }
            
            $employeeData[$userId]['pointages'][] = $pointage;
            $employeeData[$userId]['statistics']['total_pointages']++;
            
            if ($pointage->fin_pointage) {
                $employeeData[$userId]['statistics']['completed_pointages']++;
                
                // Calculer les heures travaillées
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $employeeData[$userId]['statistics']['total_hours'] += $hours;
            }
            
            // Ajouter le site aux sites travaillés
            if (!in_array($pointage->site->name, $employeeData[$userId]['statistics']['sites_worked'])) {
                $employeeData[$userId]['statistics']['sites_worked'][] = $pointage->site->name;
            }
        }

        // Calculer les moyennes
        foreach ($employeeData as &$data) {
            $totalDays = $startDate->diffInDays($endDate) + 1;
            $data['statistics']['average_daily_hours'] = $totalDays > 0 
                ? round($data['statistics']['total_hours'] / $totalDays, 2) 
                : 0;
        }

        return [
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'employees' => $employeeData,
            'summary' => $this->calculateGlobalSummary($employeeData)
        ];
    }

    /**
     * Prépare les données pour un employé individuel
     */
    private function prepareIndividualEmployeeData(User $user, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['site'])
            ->where('user_id', $user->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Calculer les statistiques détaillées
        $statistics = [
            'total_pointages' => $pointages->count(),
            'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
            'active_pointages' => $pointages->whereNull('fin_pointage')->count(),
            'total_hours' => 0,
            'average_daily_hours' => 0,
            'sites_worked' => [],
            'daily_breakdown' => []
        ];

        // Analyser chaque pointage
        foreach ($pointages as $pointage) {
            $date = Carbon::parse($pointage->debut_pointage)->format('Y-m-d');
            
            if (!isset($statistics['daily_breakdown'][$date])) {
                $statistics['daily_breakdown'][$date] = [
                    'date' => $date,
                    'pointages' => 0,
                    'hours' => 0,
                    'sites' => []
                ];
            }
            
            $statistics['daily_breakdown'][$date]['pointages']++;
            
            if ($pointage->fin_pointage) {
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $statistics['total_hours'] += $hours;
                $statistics['daily_breakdown'][$date]['hours'] += $hours;
            }
            
            if (!in_array($pointage->site->name, $statistics['sites_worked'])) {
                $statistics['sites_worked'][] = $pointage->site->name;
            }
            
            if (!in_array($pointage->site->name, $statistics['daily_breakdown'][$date]['sites'])) {
                $statistics['daily_breakdown'][$date]['sites'][] = $pointage->site->name;
            }
        }

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $statistics['average_daily_hours'] = $totalDays > 0 
            ? round($statistics['total_hours'] / $totalDays, 2) 
            : 0;

        return [
            'user' => $user,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'statistics' => $statistics
        ];
    }

    /**
     * Prépare les données pour un rapport de site
     */
    private function prepareSiteReportData(Site $site, Carbon $startDate, Carbon $endDate): array
    {
        $pointages = Pointage::with(['user'])
            ->where('site_id', $site->id)
            ->whereBetween('debut_pointage', [$startDate, $endDate])
            ->orderBy('debut_pointage', 'desc')
            ->get();

        // Analyser par employé sur ce site
        $employeeStats = [];
        
        foreach ($pointages as $pointage) {
            $userId = $pointage->user_id;
            
            if (!isset($employeeStats[$userId])) {
                $employeeStats[$userId] = [
                    'user' => $pointage->user,
                    'pointages_count' => 0,
                    'total_hours' => 0,
                    'first_pointage' => null,
                    'last_pointage' => null
                ];
            }
            
            $employeeStats[$userId]['pointages_count']++;
            
            if ($pointage->fin_pointage) {
                $hours = Carbon::parse($pointage->debut_pointage)
                    ->diffInHours(Carbon::parse($pointage->fin_pointage));
                $employeeStats[$userId]['total_hours'] += $hours;
            }
            
            if (!$employeeStats[$userId]['first_pointage'] || 
                $pointage->debut_pointage < $employeeStats[$userId]['first_pointage']) {
                $employeeStats[$userId]['first_pointage'] = $pointage->debut_pointage;
            }
            
            if (!$employeeStats[$userId]['last_pointage'] || 
                $pointage->debut_pointage > $employeeStats[$userId]['last_pointage']) {
                $employeeStats[$userId]['last_pointage'] = $pointage->debut_pointage;
            }
        }

        return [
            'site' => $site,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'pointages' => $pointages,
            'employee_statistics' => $employeeStats,
            'summary' => [
                'total_pointages' => $pointages->count(),
                'unique_employees' => count($employeeStats),
                'total_hours' => array_sum(array_column($employeeStats, 'total_hours'))
            ]
        ];
    }

    /**
     * Calcule un résumé global
     */
    private function calculateGlobalSummary(array $employeeData): array
    {
        $totalEmployees = count($employeeData);
        $totalPointages = array_sum(array_column(array_column($employeeData, 'statistics'), 'total_pointages'));
        $totalHours = array_sum(array_column(array_column($employeeData, 'statistics'), 'total_hours'));
        
        return [
            'total_employees' => $totalEmployees,
            'total_pointages' => $totalPointages,
            'total_hours' => $totalHours,
            'average_hours_per_employee' => $totalEmployees > 0 ? round($totalHours / $totalEmployees, 2) : 0
        ];
    }
}
