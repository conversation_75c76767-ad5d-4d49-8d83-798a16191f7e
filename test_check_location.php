<?php

// Script de test pour l'endpoint /pointage/check-location
echo "=== Test de l'endpoint /pointage/check-location ===\n";

// Configuration
$baseUrl = 'http://127.0.0.1:8000';
$loginEndpoint = '/api/auth/login';
$checkLocationEndpoint = '/api/pointage/check-location';

// Identifiants de test
$credentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

// Coordonnées de test (Casablanca, Maroc)
$testLocations = [
    'valid_location' => [
        'latitude' => 33.5731,
        'longitude' => -7.5898,
        'description' => 'Casablanca Centre (position valide)'
    ],
    'nearby_location' => [
        'latitude' => 33.5741,
        'longitude' => -7.5908,
        'description' => 'Proche du site (dans la zone)'
    ],
    'far_location' => [
        'latitude' => 33.6731,
        'longitude' => -7.6898,
        'description' => 'Loin du site (hors zone)'
    ],
    'rabat_location' => [
        'latitude' => 34.0209,
        'longitude' => -6.8416,
        'description' => 'Rabat (très loin)'
    ]
];

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $context = [
        'http' => [
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'timeout' => 30
        ]
    ];
    
    if ($data && $method === 'POST') {
        $context['http']['content'] = json_encode($data);
    }
    
    $response = file_get_contents($url, false, stream_context_create($context));
    
    if ($response === false) {
        throw new Exception("Erreur lors de la requête vers $url");
    }
    
    return json_decode($response, true);
}

try {
    echo "\n1. Connexion et récupération du token...\n";
    
    $loginResponse = makeRequest(
        $baseUrl . $loginEndpoint,
        'POST',
        $credentials,
        [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    );
    
    if (!isset($loginResponse['success']) || !$loginResponse['success']) {
        throw new Exception("Échec de la connexion: " . ($loginResponse['message'] ?? 'Erreur inconnue'));
    }
    
    $token = $loginResponse['data']['token'];
    $user = $loginResponse['data']['user'];
    
    echo "✅ Connexion réussie\n";
    echo "   Utilisateur: {$user['name']} ({$user['email']})\n";
    echo "   Token: " . substr($token, 0, 20) . "...\n";
    
    echo "\n2. Test de vérification de localisation...\n";
    
    foreach ($testLocations as $key => $location) {
        echo "\n--- Test: {$location['description']} ---\n";
        echo "Coordonnées: {$location['latitude']}, {$location['longitude']}\n";
        
        try {
            $checkResponse = makeRequest(
                $baseUrl . $checkLocationEndpoint,
                'POST',
                [
                    'latitude' => $location['latitude'],
                    'longitude' => $location['longitude'],
                    'accuracy' => 10.0,
                    'timestamp' => time()
                ],
                [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'Authorization: Bearer ' . $token
                ]
            );
            
            echo "Réponse:\n";
            echo json_encode($checkResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if (isset($checkResponse['success']) && $checkResponse['success']) {
                $withinRange = $checkResponse['within_range'] ?? false;
                $distance = $checkResponse['distance'] ?? 'N/A';
                $site = $checkResponse['site'] ?? 'N/A';
                $canPointe = $checkResponse['can_pointe'] ?? false;
                
                echo "✅ Requête réussie\n";
                echo "   Site: $site\n";
                echo "   Distance: {$distance}m\n";
                echo "   Dans la zone: " . ($withinRange ? 'OUI' : 'NON') . "\n";
                echo "   Peut pointer: " . ($canPointe ? 'OUI' : 'NON') . "\n";
            } else {
                echo "❌ Erreur: " . ($checkResponse['message'] ?? 'Erreur inconnue') . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur lors de la requête: " . $e->getMessage() . "\n";
        }
        
        echo str_repeat("-", 50) . "\n";
    }
    
    echo "\n3. Test avec des données invalides...\n";
    
    $invalidTests = [
        [
            'name' => 'Latitude manquante',
            'data' => ['longitude' => -7.5898]
        ],
        [
            'name' => 'Longitude manquante',
            'data' => ['latitude' => 33.5731]
        ],
        [
            'name' => 'Latitude invalide',
            'data' => ['latitude' => 91.0, 'longitude' => -7.5898]
        ],
        [
            'name' => 'Longitude invalide',
            'data' => ['latitude' => 33.5731, 'longitude' => 181.0]
        ],
        [
            'name' => 'Coordonnées 0,0',
            'data' => ['latitude' => 0.0, 'longitude' => 0.0]
        ]
    ];
    
    foreach ($invalidTests as $test) {
        echo "\n--- Test: {$test['name']} ---\n";
        
        try {
            $response = makeRequest(
                $baseUrl . $checkLocationEndpoint,
                'POST',
                $test['data'],
                [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'Authorization: Bearer ' . $token
                ]
            );
            
            echo "Réponse:\n";
            echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "FIN DES TESTS\n";
echo str_repeat("=", 60) . "\n";
