<?php

namespace App\Http\Controllers\Monitoring;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiResponseTrait;
use App\Services\EmployeePresenceMonitoringService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * @group Employee Monitoring
 *
 * APIs pour la surveillance de présence des employés sur leurs chantiers assignés
 */
class EmployeeMonitoringController extends Controller
{
    use ApiResponseTrait;

    private EmployeePresenceMonitoringService $monitoringService;

    public function __construct(EmployeePresenceMonitoringService $monitoringService)
    {
        $this->monitoringService = $monitoringService;
    }

    /**
     * Vérifier si un employé est sur son chantier assigné
     *
     * @authenticated
     * @bodyParam user_id integer required ID de l'employé à vérifier. Example: 1
     * @bodyParam latitude float required Latitude actuelle de l'employé. Example: 33.5731
     * @bodyParam longitude float required Longitude actuelle de l'employé. Example: -7.5898
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "employee": {
     *       "id": 1,
     *       "name": "<PERSON> Benali",
     *       "email": "<EMAIL>",
     *       "role": "employee"
     *     },
     *     "assigned_site": {
     *       "id": 1,
     *       "name": "Chantier Principal Casablanca",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     },
     *     "current_position": {
     *       "latitude": 33.5735,
     *       "longitude": -7.5902
     *     },
     *     "is_on_assigned_site": true,
     *     "distance_from_site": 45.2,
     *     "max_allowed_distance": 50,
     *     "presence_status": "on_site_authorized",
     *     "check_time": "2024-01-15T10:30:00Z",
     *     "message": "Ahmed Benali est présent sur le chantier Chantier Principal Casablanca (à 45.2m)",
     *     "message_ar": "أحمد بنعلي موجود في موقع العمل الرئيسي الدار البيضاء (على بعد 45.2م)"
     *   }
     * }
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "employee": {
     *       "id": 2,
     *       "name": "Fatima Zahra",
     *       "email": "<EMAIL>",
     *       "role": "employee"
     *     },
     *     "assigned_site": {
     *       "id": 1,
     *       "name": "Chantier Principal Casablanca",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     },
     *     "current_position": {
     *       "latitude": 34.0209,
     *       "longitude": -6.8416
     *     },
     *     "is_on_assigned_site": false,
     *     "distance_from_site": 85432.15,
     *     "max_allowed_distance": 50,
     *     "presence_status": "very_far_from_site",
     *     "check_time": "2024-01-15T10:30:00Z",
     *     "message": "🚨 Fatima Zahra est très éloignée du chantier Chantier Principal Casablanca (à 85432.15m)",
     *     "message_ar": "🚨 فاطمة الزهراء بعيدة جداً عن موقع العمل الرئيسي الدار البيضاء (على بعد 85432.15م)"
     *   }
     * }
     */
    public function checkEmployeeOnSite(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $result = $this->monitoringService->checkEmployeeOnAssignedSite(
                $request->user_id,
                $request->latitude,
                $request->longitude
            );

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'Vérification de présence effectuée.',
                    'تم التحقق من الحضور.'
                );
            } else {
                return $this->errorResponse(
                    $result['message'],
                    $result['message_ar'] ?? 'خطأ في التحقق من الحضور.',
                    400
                );
            }

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la vérification de présence.',
                'خطأ أثناء التحقق من الحضور.',
                500,
                $e
            );
        }
    }

    /**
     * Vérifier tous les employés actifs sur leurs chantiers
     *
     * @authenticated
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "summary": {
     *       "total_checked": 5,
     *       "present_on_site": 3,
     *       "absent_from_site": 1,
     *       "no_assigned_site": 1,
     *       "notifications_sent": 1
     *     },
     *     "detailed_results": [
     *       {
     *         "pointage_id": 1,
     *         "started_at": "2024-01-15T08:00:00Z",
     *         "check_result": {
     *           "employee": {
     *             "id": 1,
     *             "name": "Ahmed Benali"
     *           },
     *           "is_on_assigned_site": true,
     *           "distance_from_site": 25.5
     *         }
     *       }
     *     ],
     *     "check_time": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function checkAllActiveEmployees(): JsonResponse
    {
        try {
            $result = $this->monitoringService->checkAllActiveEmployeesOnSites();

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'Vérification globale effectuée.',
                    'تم التحقق الشامل.'
                );
            } else {
                return $this->errorResponse(
                    $result['message'] ?? 'Erreur lors de la vérification globale.',
                    'خطأ أثناء التحقق الشامل.',
                    500
                );
            }

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la vérification globale.',
                'خطأ أثناء التحقق الشامل.',
                500,
                $e
            );
        }
    }

    /**
     * Démarrer la surveillance d'un employé
     *
     * @authenticated
     * @bodyParam user_id integer required ID de l'employé à surveiller. Example: 1
     * @bodyParam interval_minutes integer optional Intervalle de vérification en minutes (défaut: 15). Example: 15
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "message": "Surveillance démarrée pour Ahmed Benali",
     *     "message_ar": "تم بدء المراقبة للموظف أحمد بنعلي",
     *     "monitoring_config": {
     *       "user_id": 1,
     *       "interval_minutes": 15,
     *       "started_at": "2024-01-15T10:30:00Z",
     *       "expires_at": "2024-01-15T22:30:00Z"
     *     }
     *   }
     * }
     */
    public function startMonitoring(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'interval_minutes' => 'integer|min:5|max:60'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $result = $this->monitoringService->startEmployeeMonitoring(
                $request->user_id,
                $request->input('interval_minutes', 15)
            );

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'Surveillance démarrée.',
                    'تم بدء المراقبة.'
                );
            } else {
                return $this->errorResponse(
                    $result['message'],
                    $result['message_ar'] ?? 'خطأ في بدء المراقبة.',
                    400
                );
            }

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors du démarrage de la surveillance.',
                'خطأ أثناء بدء المراقبة.',
                500,
                $e
            );
        }
    }

    /**
     * Arrêter la surveillance d'un employé
     *
     * @authenticated
     * @bodyParam user_id integer required ID de l'employé. Example: 1
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "message": "Surveillance arrêtée pour Ahmed Benali",
     *     "message_ar": "تم إيقاف المراقبة للموظف أحمد بنعلي",
     *     "monitoring_summary": {
     *       "duration_minutes": 120,
     *       "stopped_at": "2024-01-15T12:30:00Z"
     *     }
     *   }
     * }
     */
    public function stopMonitoring(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $result = $this->monitoringService->stopEmployeeMonitoring($request->user_id);

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'Surveillance arrêtée.',
                    'تم إيقاف المراقبة.'
                );
            } else {
                return $this->errorResponse(
                    $result['message'],
                    $result['message_ar'] ?? 'خطأ في إيقاف المراقبة.',
                    400
                );
            }

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de l\'arrêt de la surveillance.',
                'خطأ أثناء إيقاف المراقبة.',
                500,
                $e
            );
        }
    }

    /**
     * Obtenir le statut de surveillance des employés
     *
     * @authenticated
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "active_monitoring": [
     *       {
     *         "user_id": 1,
     *         "user_name": "Ahmed Benali",
     *         "started_at": "2024-01-15T10:30:00Z",
     *         "interval_minutes": 15,
     *         "expires_at": "2024-01-15T22:30:00Z"
     *       }
     *     ],
     *     "total_monitored": 1
     *   }
     * }
     */
    public function getMonitoringStatus(): JsonResponse
    {
        try {
            // Récupérer tous les employés surveillés depuis le cache
            $activeMonitoring = [];
            $cacheKeys = \Illuminate\Support\Facades\Cache::getRedis()->keys('*monitoring:user:*');
            
            foreach ($cacheKeys as $key) {
                if (preg_match('/monitoring:user:(\d+)/', $key, $matches)) {
                    $userId = $matches[1];
                    $monitoringData = \Illuminate\Support\Facades\Cache::get($key);
                    
                    if ($monitoringData) {
                        $user = \App\Models\User::find($userId);
                        if ($user) {
                            $activeMonitoring[] = [
                                'user_id' => $userId,
                                'user_name' => $user->name,
                                'started_at' => $monitoringData['started_at'],
                                'interval_minutes' => $monitoringData['interval_minutes'],
                                'expires_at' => $monitoringData['started_at']->addHours(12)
                            ];
                        }
                    }
                }
            }

            return $this->successResponse([
                'active_monitoring' => $activeMonitoring,
                'total_monitored' => count($activeMonitoring)
            ], 'Statut de surveillance récupéré.', 'تم استرداد حالة المراقبة.');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la récupération du statut.',
                'خطأ أثناء استرداد الحالة.',
                500,
                $e
            );
        }
    }
}
