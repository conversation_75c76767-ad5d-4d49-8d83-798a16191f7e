<?php

// Script pour vérifier la structure de la table users
echo "=== Vérification de la table users ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "1. Connexion à la base de données...\n";
    DB::connection()->getPdo();
    echo "✅ Connexion réussie\n";

    echo "\n2. Structure de la table users...\n";
    
    if (Schema::hasTable('users')) {
        // Obtenir la structure complète
        $columns = DB::select("DESCRIBE users");
        
        echo "Colonnes de la table users:\n";
        foreach ($columns as $column) {
            echo sprintf("  %-20s %-15s %-10s %-10s %-10s %s\n", 
                $column->Field, 
                $column->Type, 
                $column->Null, 
                $column->Key, 
                $column->Default ?? 'NULL',
                $column->Extra ?? ''
            );
        }
        
        echo "\n3. Données utilisateur existantes...\n";
        $users = DB::table('users')->get();
        
        if ($users->count() > 0) {
            echo "Utilisateurs trouvés:\n";
            foreach ($users as $user) {
                echo "  ID: {$user->id}\n";
                echo "  Name: {$user->name}\n";
                echo "  Email: {$user->email}\n";
                echo "  Role: " . (isset($user->role) ? $user->role : 'NON DÉFINI') . "\n";
                echo "  Email Verified: " . (isset($user->email_verified_at) ? ($user->email_verified_at ?? 'NULL') : 'COLONNE MANQUANTE') . "\n";
                echo "  Created: " . (isset($user->created_at) ? $user->created_at : 'NON DÉFINI') . "\n";
                echo "  ---\n";
            }
        } else {
            echo "Aucun utilisateur trouvé.\n";
        }
        
        echo "\n4. Vérification des colonnes manquantes...\n";
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = [
            'id' => 'bigint unsigned auto_increment primary key',
            'name' => 'varchar(191) not null',
            'email' => 'varchar(191) not null unique',
            'email_verified_at' => 'timestamp null',
            'password' => 'varchar(191) not null',
            'role' => 'varchar(191) default \'employee\'',
            'remember_token' => 'varchar(100) null',
            'created_at' => 'timestamp null',
            'updated_at' => 'timestamp null'
        ];
        
        $missingColumns = [];
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columnNames)) {
                $missingColumns[] = $column;
                echo "❌ Colonne manquante: $column ($definition)\n";
            } else {
                echo "✅ Colonne présente: $column\n";
            }
        }
        
        if (!empty($missingColumns)) {
            echo "\n5. Ajout des colonnes manquantes...\n";
            
            foreach ($missingColumns as $column) {
                try {
                    switch ($column) {
                        case 'email_verified_at':
                            DB::statement("ALTER TABLE users ADD COLUMN email_verified_at TIMESTAMP NULL");
                            echo "✅ Colonne email_verified_at ajoutée\n";
                            break;
                        case 'role':
                            DB::statement("ALTER TABLE users ADD COLUMN role VARCHAR(191) DEFAULT 'employee'");
                            echo "✅ Colonne role ajoutée\n";
                            break;
                        case 'remember_token':
                            DB::statement("ALTER TABLE users ADD COLUMN remember_token VARCHAR(100) NULL");
                            echo "✅ Colonne remember_token ajoutée\n";
                            break;
                        default:
                            echo "⚠️  Colonne $column nécessite une attention manuelle\n";
                    }
                } catch (Exception $e) {
                    echo "❌ Erreur lors de l'ajout de $column: " . $e->getMessage() . "\n";
                }
            }
        } else {
            echo "✅ Toutes les colonnes requises sont présentes\n";
        }
        
    } else {
        echo "❌ Table users n'existe pas!\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
