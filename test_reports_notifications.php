<?php

// Script de test pour les rapports Excel et notifications
echo "=== Test des Rapports Excel et Notifications ===\n";

// Configuration
$baseUrl = 'http://127.0.0.1:8000';
$loginEndpoint = '/api/auth/login';

// Identifiants admin
$credentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $context = [
        'http' => [
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'timeout' => 30
        ]
    ];
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        $context['http']['content'] = json_encode($data);
    }
    
    $response = file_get_contents($url, false, stream_context_create($context));
    
    if ($response === false) {
        throw new Exception("Erreur lors de la requête vers $url");
    }
    
    return json_decode($response, true);
}

try {
    echo "\n1. Connexion en tant qu'admin...\n";
    
    $loginResponse = makeRequest(
        $baseUrl . $loginEndpoint,
        'POST',
        $credentials,
        [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    );
    
    if (!isset($loginResponse['success']) || !$loginResponse['success']) {
        throw new Exception("Échec de la connexion: " . ($loginResponse['message'] ?? 'Erreur inconnue'));
    }
    
    $token = $loginResponse['data']['token'];
    $user = $loginResponse['data']['user'];
    
    echo "✅ Connexion réussie en tant que {$user['name']}\n";
    
    $authHeaders = [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ];

    echo "\n2. Test de génération de rapport Excel global...\n";
    
    $reportData = [
        'start_date' => '2024-01-01',
        'end_date' => '2024-12-31',
        'include_stats' => true
    ];
    
    try {
        $reportResponse = makeRequest(
            $baseUrl . '/api/reports/employees',
            'POST',
            $reportData,
            $authHeaders
        );
        
        if ($reportResponse['success']) {
            echo "✅ Rapport global généré avec succès\n";
            echo "   Fichier: {$reportResponse['data']['filename']}\n";
            echo "   Taille: {$reportResponse['data']['file_size']}\n";
            echo "   URL de téléchargement: {$reportResponse['data']['download_url']}\n";
        } else {
            echo "❌ Erreur génération rapport: {$reportResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la génération du rapport: " . $e->getMessage() . "\n";
    }

    echo "\n3. Test de génération de rapport individuel...\n";
    
    try {
        $individualReportResponse = makeRequest(
            $baseUrl . '/api/reports/employees/1',
            'POST',
            $reportData,
            $authHeaders
        );
        
        if ($individualReportResponse['success']) {
            echo "✅ Rapport individuel généré avec succès\n";
            echo "   Fichier: {$individualReportResponse['data']['filename']}\n";
            echo "   Taille: {$individualReportResponse['data']['file_size']}\n";
        } else {
            echo "❌ Erreur génération rapport individuel: {$individualReportResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la génération du rapport individuel: " . $e->getMessage() . "\n";
    }

    echo "\n4. Test de génération de rapport par site...\n";
    
    try {
        $siteReportResponse = makeRequest(
            $baseUrl . '/api/reports/sites/1',
            'POST',
            $reportData,
            $authHeaders
        );
        
        if ($siteReportResponse['success']) {
            echo "✅ Rapport de site généré avec succès\n";
            echo "   Fichier: {$siteReportResponse['data']['filename']}\n";
            echo "   Taille: {$siteReportResponse['data']['file_size']}\n";
        } else {
            echo "❌ Erreur génération rapport site: {$siteReportResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la génération du rapport site: " . $e->getMessage() . "\n";
    }

    echo "\n5. Test de vérification de présence d'employé...\n";
    
    $presenceData = [
        'user_id' => 1,
        'latitude' => 33.5731,
        'longitude' => -7.5898,
        'send_alert' => true
    ];
    
    try {
        $presenceResponse = makeRequest(
            $baseUrl . '/api/reports/verify-presence',
            'POST',
            $presenceData,
            $authHeaders
        );
        
        if ($presenceResponse['success']) {
            echo "✅ Vérification de présence effectuée\n";
            $data = $presenceResponse['data'];
            echo "   Présent: " . ($data['is_present'] ? 'OUI' : 'NON') . "\n";
            echo "   Distance: {$data['distance']}m\n";
            echo "   Site: {$data['site']['name']}\n";
            echo "   Statut: {$data['status']}\n";
            echo "   Message: {$data['message']}\n";
        } else {
            echo "❌ Erreur vérification présence: {$presenceResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification de présence: " . $e->getMessage() . "\n";
    }

    echo "\n6. Test de vérification de présence (position éloignée)...\n";
    
    $presenceDataFar = [
        'user_id' => 1,
        'latitude' => 34.0209, // Rabat (loin)
        'longitude' => -6.8416,
        'send_alert' => true
    ];
    
    try {
        $presenceFarResponse = makeRequest(
            $baseUrl . '/api/reports/verify-presence',
            'POST',
            $presenceDataFar,
            $authHeaders
        );
        
        if ($presenceFarResponse['success']) {
            echo "✅ Vérification de présence (éloignée) effectuée\n";
            $data = $presenceFarResponse['data'];
            echo "   Présent: " . ($data['is_present'] ? 'OUI' : 'NON') . "\n";
            echo "   Distance: {$data['distance']}m\n";
            echo "   Statut: {$data['status']}\n";
            echo "   Message: {$data['message']}\n";
        } else {
            echo "❌ Erreur vérification présence éloignée: {$presenceFarResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification de présence éloignée: " . $e->getMessage() . "\n";
    }

    echo "\n7. Test de vérification de tous les employés actifs...\n";
    
    try {
        $allEmployeesResponse = makeRequest(
            $baseUrl . '/api/reports/check-all-employees',
            'POST',
            [],
            $authHeaders
        );
        
        if ($allEmployeesResponse['success']) {
            echo "✅ Vérification globale effectuée\n";
            $data = $allEmployeesResponse['data'];
            echo "   Total vérifié: {$data['total_checked']}\n";
            echo "   Présents: {$data['present_count']}\n";
            echo "   Absents: {$data['absent_count']}\n";
            
            if (!empty($data['verifications'])) {
                echo "   Détails des vérifications:\n";
                foreach ($data['verifications'] as $verification) {
                    echo "     - {$verification['user']} sur {$verification['site']}: " . 
                         ($verification['verification']['is_present'] ? 'PRÉSENT' : 'ABSENT') . "\n";
                }
            }
        } else {
            echo "❌ Erreur vérification globale: {$allEmployeesResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification globale: " . $e->getMessage() . "\n";
    }

    echo "\n8. Test de téléchargement de rapport (si disponible)...\n";
    
    if (isset($reportResponse) && $reportResponse['success']) {
        $filename = $reportResponse['data']['filename'];
        
        try {
            // Simuler le téléchargement (juste vérifier que l'endpoint répond)
            $downloadUrl = $baseUrl . "/api/reports/download/{$filename}";
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => "Authorization: Bearer {$token}\r\n",
                    'timeout' => 10
                ]
            ]);
            
            $headers = get_headers($downloadUrl, 1, $context);
            
            if (strpos($headers[0], '200') !== false) {
                echo "✅ Endpoint de téléchargement accessible\n";
                echo "   URL: {$downloadUrl}\n";
            } else {
                echo "❌ Endpoint de téléchargement non accessible: {$headers[0]}\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur lors du test de téléchargement: " . $e->getMessage() . "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "FIN DES TESTS\n";
echo str_repeat("=", 60) . "\n";

echo "\n📋 RÉSUMÉ DES FONCTIONNALITÉS TESTÉES:\n";
echo "✅ Génération de rapport Excel global des employés\n";
echo "✅ Génération de rapport Excel individuel\n";
echo "✅ Génération de rapport Excel par site\n";
echo "✅ Vérification de présence d'employé\n";
echo "✅ Système d'alertes pour absence\n";
echo "✅ Vérification globale de tous les employés actifs\n";
echo "✅ Téléchargement de rapports\n";

echo "\n🎯 ENDPOINTS DISPONIBLES:\n";
echo "POST /api/reports/employees - Rapport global\n";
echo "POST /api/reports/employees/{id} - Rapport individuel\n";
echo "POST /api/reports/sites/{id} - Rapport par site\n";
echo "POST /api/reports/verify-presence - Vérifier présence\n";
echo "POST /api/reports/check-all-employees - Vérifier tous\n";
echo "GET /api/reports/download/{filename} - Télécharger\n";
