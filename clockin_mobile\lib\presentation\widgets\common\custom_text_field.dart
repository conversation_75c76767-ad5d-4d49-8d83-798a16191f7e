import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';

class CustomTextField extends StatelessWidget {
  final String name;
  final String label;
  final String? hintText;
  final String? initialValue;
  final TextInputType keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final List<String? Function(String?)> validators;
  final void Function(String?)? onChanged;
  final void Function()? onTap;
  final TextEditingController? controller;

  const CustomTextField({
    super.key,
    required this.name,
    required this.label,
    this.hintText,
    this.initialValue,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.validators = const [],
    this.onChanged,
    this.onTap,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingS),
        
        // Text Field
        FormBuilderTextField(
          name: name,
          initialValue: initialValue,
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          maxLength: maxLength,
          onChanged: onChanged,
          onTap: onTap,
          validator: validators.isNotEmpty
              ? (value) {
                  for (final validator in validators) {
                    final result = validator(value);
                    if (result != null) return result;
                  }
                  return null;
                }
              : null,
          decoration: InputDecoration(
            hintText: hintText ?? 'Enter $label',
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.grey500,
                  )
                : null,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: enabled ? AppColors.grey50 : AppColors.grey100,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.grey300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.grey300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.primaryBlue, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              borderSide: const BorderSide(color: AppColors.grey200),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacingM,
              vertical: AppConstants.spacingM,
            ),
          ),
        ),
      ],
    );
  }
}

// Specialized text field variants
class EmailTextField extends CustomTextField {
  EmailTextField({
    super.key,
    required super.name,
    super.label = 'Email',
    super.hintText = 'Enter your email address',
    super.initialValue,
    super.onChanged,
    super.controller,
  }) : super(
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icons.email_outlined,
        );
}

class PasswordTextField extends CustomTextField {
  PasswordTextField({
    super.key,
    required super.name,
    super.label = 'Password',
    super.hintText = 'Enter your password',
    super.initialValue,
    super.onChanged,
    super.controller,
    Widget? suffixIcon,
  }) : super(
          obscureText: true,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: suffixIcon,
        );
}

class PhoneTextField extends CustomTextField {
  PhoneTextField({
    super.key,
    required super.name,
    super.label = 'Phone Number',
    super.hintText = 'Enter your phone number',
    super.initialValue,
    super.onChanged,
    super.controller,
  }) : super(
          keyboardType: TextInputType.phone,
          prefixIcon: Icons.phone_outlined,
        );
}

class SearchTextField extends CustomTextField {
  SearchTextField({
    super.key,
    required super.name,
    super.label = 'Search',
    super.hintText = 'Search...',
    super.initialValue,
    super.onChanged,
    super.controller,
  }) : super(
          prefixIcon: Icons.search_outlined,
        );
}

class MultilineTextField extends CustomTextField {
  MultilineTextField({
    super.key,
    required super.name,
    required super.label,
    super.hintText,
    super.initialValue,
    super.onChanged,
    super.controller,
    int maxLines = 3,
  }) : super(
          maxLines: maxLines,
          keyboardType: TextInputType.multiline,
        );
}
