# Guide Postman - Surveillance des Employés sur Chantiers

## 🎯 Objectif
Système de surveillance en temps réel pour vérifier si les employés sont bien présents sur leurs chantiers assignés avec notifications automatiques.

## 🔐 Prérequis

### Authentification Admin Requise
Toutes les fonctionnalités de surveillance nécessitent un token admin.

**POST** `http://127.0.0.1:8000/api/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Headers Standards
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN
```

## 🔍 Vérification de Présence sur Chantier Assigné

### 1. Vérifier un Employé Spécifique

**POST** `http://127.0.0.1:8000/api/monitoring/check-employee-on-site`

#### Cas 1: Employé Présent sur son Chantier
**Body:**
```json
{
  "user_id": 2,
  "latitude": 33.5731,
  "longitude": -7.5898
}
```

**Réponse (Présent):**
```json
{
  "success": true,
  "data": {
    "employee": {
      "id": 2,
      "name": "Ahmed Benali",
      "email": "<EMAIL>",
      "role": "employee"
    },
    "assigned_site": {
      "id": 1,
      "name": "Chantier Principal Casablanca",
      "latitude": 33.5731,
      "longitude": -7.5898
    },
    "current_position": {
      "latitude": 33.5731,
      "longitude": -7.5898
    },
    "is_on_assigned_site": true,
    "distance_from_site": 0.0,
    "max_allowed_distance": 50,
    "presence_status": "on_site_exact",
    "check_time": "2024-01-15T10:30:00Z",
    "message": "Ahmed Benali est présent sur le chantier Chantier Principal Casablanca (position exacte)",
    "message_ar": "أحمد بنعلي موجود في موقع العمل الرئيسي الدار البيضاء (الموقع الدقيق)"
  }
}
```

#### Cas 2: Employé Absent de son Chantier
**Body:**
```json
{
  "user_id": 2,
  "latitude": 34.0209,
  "longitude": -6.8416
}
```

**Réponse (Absent avec Notification):**
```json
{
  "success": true,
  "data": {
    "employee": {
      "id": 2,
      "name": "Ahmed Benali",
      "email": "<EMAIL>",
      "role": "employee"
    },
    "assigned_site": {
      "id": 1,
      "name": "Chantier Principal Casablanca",
      "latitude": 33.5731,
      "longitude": -7.5898
    },
    "current_position": {
      "latitude": 34.0209,
      "longitude": -6.8416
    },
    "is_on_assigned_site": false,
    "distance_from_site": 85432.15,
    "max_allowed_distance": 50,
    "presence_status": "very_far_from_site",
    "check_time": "2024-01-15T10:30:00Z",
    "message": "🚨 Ahmed Benali est très éloigné du chantier Chantier Principal Casablanca (à 85432.15m)",
    "message_ar": "🚨 أحمد بنعلي بعيد جداً عن موقع العمل الرئيسي الدار البيضاء (على بعد 85432.15م)"
  }
}
```

#### Cas 3: Employé Sans Chantier Assigné
**Réponse:**
```json
{
  "success": false,
  "message": "Aucun chantier assigné à l'employé Ahmed Benali",
  "message_ar": "لا يوجد موقع عمل مخصص للموظف أحمد بنعلي",
  "employee": {
    "id": 2,
    "name": "Ahmed Benali",
    "email": "<EMAIL>"
  },
  "is_on_assigned_site": false,
  "assigned_site": null
}
```

### 2. Vérifier Tous les Employés Actifs

**POST** `http://127.0.0.1:8000/api/monitoring/check-all-active`

**Body:** `{}` (vide)

**Réponse:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_checked": 5,
      "present_on_site": 3,
      "absent_from_site": 1,
      "no_assigned_site": 1,
      "notifications_sent": 1
    },
    "detailed_results": [
      {
        "pointage_id": 1,
        "started_at": "2024-01-15T08:00:00Z",
        "check_result": {
          "employee": {
            "id": 2,
            "name": "Ahmed Benali",
            "email": "<EMAIL>",
            "role": "employee"
          },
          "is_on_assigned_site": true,
          "distance_from_site": 25.5,
          "presence_status": "on_site_authorized"
        }
      }
    ],
    "check_time": "2024-01-15T10:30:00Z"
  }
}
```

## 📡 Surveillance Continue

### 3. Démarrer la Surveillance d'un Employé

**POST** `http://127.0.0.1:8000/api/monitoring/start-monitoring`

**Body:**
```json
{
  "user_id": 2,
  "interval_minutes": 15
}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "message": "Surveillance démarrée pour Ahmed Benali",
    "message_ar": "تم بدء المراقبة للموظف أحمد بنعلي",
    "monitoring_config": {
      "user_id": 2,
      "interval_minutes": 15,
      "started_at": "2024-01-15T10:30:00Z",
      "expires_at": "2024-01-15T22:30:00Z"
    }
  }
}
```

### 4. Arrêter la Surveillance

**POST** `http://127.0.0.1:8000/api/monitoring/stop-monitoring`

**Body:**
```json
{
  "user_id": 2
}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "message": "Surveillance arrêtée pour Ahmed Benali",
    "message_ar": "تم إيقاف المراقبة للموظف أحمد بنعلي",
    "monitoring_summary": {
      "duration_minutes": 120,
      "stopped_at": "2024-01-15T12:30:00Z"
    }
  }
}
```

### 5. Statut de Surveillance

**GET** `http://127.0.0.1:8000/api/monitoring/status`

**Réponse:**
```json
{
  "success": true,
  "data": {
    "active_monitoring": [
      {
        "user_id": 2,
        "user_name": "Ahmed Benali",
        "started_at": "2024-01-15T10:30:00Z",
        "interval_minutes": 15,
        "expires_at": "2024-01-15T22:30:00Z"
      }
    ],
    "total_monitored": 1
  }
}
```

## 🔔 Statuts de Présence

| Statut | Distance | Description | Notification |
|--------|----------|-------------|--------------|
| `on_site_exact` | 0-10m | Position exacte | ✅ Aucune |
| `on_site_authorized` | 10-50m | Sur site autorisé | ✅ Aucune |
| `nearby_site` | 50-100m | Proche du site | ⚠️ Avertissement |
| `far_from_site` | 100-500m | Éloigné du site | 🚨 Alerte |
| `very_far_from_site` | >500m | Très éloigné | 🚨 Alerte urgente |

## 🎯 Cas d'Usage Pratiques

### Surveillance Matinale
```bash
# 1. Vérifier tous les employés au début de journée
POST /api/monitoring/check-all-active

# 2. Démarrer surveillance pour employés critiques
POST /api/monitoring/start-monitoring
{
  "user_id": 2,
  "interval_minutes": 10
}
```

### Contrôle Ponctuel
```bash
# Vérifier un employé spécifique
POST /api/monitoring/check-employee-on-site
{
  "user_id": 2,
  "latitude": 33.5735,
  "longitude": -7.5902
}
```

### Fin de Surveillance
```bash
# Arrêter toutes les surveillances
GET /api/monitoring/status
# Puis pour chaque employé surveillé:
POST /api/monitoring/stop-monitoring
```

## ⚙️ Configuration

### Paramètres Modifiables
- **Distance maximale**: 50m (configurable dans `config/clockin.php`)
- **Intervalle de surveillance**: 5-60 minutes
- **Durée de surveillance**: 12 heures maximum
- **Cache de notification**: 30 minutes (évite le spam)

### Variables d'Environnement
```env
CLOCKIN_MAX_DISTANCE=50
CLOCKIN_MONITORING_DURATION=12
CLOCKIN_NOTIFICATION_COOLDOWN=30
```

## 🔧 Script de Test Automatique

```bash
php test_employee_monitoring.php
```

## 📝 Notes Importantes

1. **Permissions**: Seuls les admins peuvent surveiller
2. **Assignations**: Les employés doivent être assignés à un chantier
3. **Pointages Actifs**: Seuls les employés avec pointage en cours sont vérifiés
4. **Notifications**: Automatiques en cas d'absence détectée
5. **Cache**: Évite les notifications répétées
6. **Logs**: Toutes les vérifications sont enregistrées

## 🚨 Gestion des Alertes

### Types d'Alertes Automatiques
- **Absence détectée**: Employé hors de son chantier
- **Distance critique**: Employé très éloigné (>500m)
- **Chantier non assigné**: Employé sans affectation

### Canaux de Notification
- **Logs système**: Enregistrement automatique
- **Logs d'alertes**: Canal spécialisé
- **Base de données**: Table `verifications`
- **Email**: Configurable (à implémenter)
- **SMS**: Configurable (à implémenter)

## 🎯 Intégration avec Autres Systèmes

### Avec le Système de Pointage
```bash
# Vérifier lors du pointage
POST /api/pointage/check-location
# Puis automatiquement:
POST /api/monitoring/check-employee-on-site
```

### Avec les Rapports
```bash
# Générer rapport de surveillance
POST /api/reports/employees
# Inclut les données de présence
```
