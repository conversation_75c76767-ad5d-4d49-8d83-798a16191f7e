# Guide Postman - Rapports Excel et Notifications

## 🎯 Nouvelles Fonctionnalités Ajoutées

### 📊 Génération de Rapports Excel
1. **Rapport Global des Employés** - Tous les employés avec statistiques
2. **Rapport Individuel** - Détails complets pour un employé
3. **Rapport par Site** - Activité sur un site spécifique

### 🔔 Système de Notifications
1. **Vérification de Présence** - Contrôler si un employé est sur site
2. **Alertes Automatiques** - Notifications en cas d'absence
3. **Vérification Globale** - Contrôler tous les employés actifs

## 🔐 Prérequis

### 1. Authentification Admin
Toutes ces fonctionnalités nécessitent un token admin.

**POST** `http://127.0.0.1:8000/api/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 2. Headers Requis
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN
```

## 📊 Génération de Rapports Excel

### 1. Rapport Global des Employés

**POST** `http://127.0.0.1:8000/api/reports/employees`

**Body:**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "include_stats": true
}
```

**Réponse:**
```json
{
  "success": true,
  "message": "Rapport généré avec succès.",
  "data": {
    "filename": "rapport_employes_2024-01-01_2024-12-31.xlsx",
    "download_url": "/api/reports/download/rapport_employes_2024-01-01_2024-12-31.xlsx",
    "file_size": "245 KB",
    "generated_at": "2024-01-15T10:30:00Z",
    "period": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    }
  }
}
```

### 2. Rapport Individuel d'Employé

**POST** `http://127.0.0.1:8000/api/reports/employees/1`

**Body:**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

**Réponse:**
```json
{
  "success": true,
  "message": "Rapport individuel généré avec succès.",
  "data": {
    "filename": "rapport_Admin_ClockIn_2024-01-01_2024-01-31.xlsx",
    "download_url": "/api/reports/download/rapport_Admin_ClockIn_2024-01-01_2024-01-31.xlsx",
    "file_size": "156 KB",
    "generated_at": "2024-01-15T10:30:00Z",
    "user_id": 1
  }
}
```

### 3. Rapport par Site

**POST** `http://127.0.0.1:8000/api/reports/sites/1`

**Body:**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

**Réponse:**
```json
{
  "success": true,
  "message": "Rapport de site généré avec succès.",
  "data": {
    "filename": "rapport_site_Site_Principal_2024-01-01_2024-01-31.xlsx",
    "download_url": "/api/reports/download/rapport_site_Site_Principal_2024-01-01_2024-01-31.xlsx",
    "file_size": "198 KB",
    "generated_at": "2024-01-15T10:30:00Z",
    "site_id": 1
  }
}
```

### 4. Téléchargement de Rapport

**GET** `http://127.0.0.1:8000/api/reports/download/rapport_employes_2024-01-01_2024-12-31.xlsx`

**Headers:**
```
Authorization: Bearer YOUR_ADMIN_TOKEN
```

**Réponse:** Fichier Excel téléchargé directement

## 🔔 Système de Notifications

### 1. Vérification de Présence d'un Employé

**POST** `http://127.0.0.1:8000/api/reports/verify-presence`

**Body (Employé Présent):**
```json
{
  "user_id": 1,
  "latitude": 33.5731,
  "longitude": -7.5898,
  "send_alert": true
}
```

**Réponse (Présent):**
```json
{
  "success": true,
  "data": {
    "is_present": true,
    "distance": 15.25,
    "max_distance": 50,
    "site": {
      "id": 1,
      "name": "Site Principal Casablanca",
      "latitude": 33.5731,
      "longitude": -7.5898
    },
    "status": "on_site_nearby",
    "verification_time": "2024-01-15T10:30:00Z",
    "message": "Employé présent sur site (à 15.25m)"
  }
}
```

**Body (Employé Absent):**
```json
{
  "user_id": 1,
  "latitude": 34.0209,
  "longitude": -6.8416,
  "send_alert": true
}
```

**Réponse (Absent):**
```json
{
  "success": true,
  "data": {
    "is_present": false,
    "distance": 85432.15,
    "max_distance": 50,
    "site": {
      "id": 1,
      "name": "Site Principal Casablanca",
      "latitude": 33.5731,
      "longitude": -7.5898
    },
    "status": "very_far_from_site",
    "verification_time": "2024-01-15T10:30:00Z",
    "message": "Employé très éloigné du site (à 85432.15m)"
  }
}
```

### 2. Vérification de Tous les Employés Actifs

**POST** `http://127.0.0.1:8000/api/reports/check-all-employees`

**Body:** `{}` (vide)

**Réponse:**
```json
{
  "success": true,
  "data": {
    "total_checked": 3,
    "present_count": 2,
    "absent_count": 1,
    "verifications": [
      {
        "pointage_id": 1,
        "user": "Admin ClockIn",
        "site": "Site Principal Casablanca",
        "started_at": "2024-01-15T08:00:00Z",
        "verification": {
          "is_present": true,
          "distance": 12.5,
          "status": "on_site_nearby"
        }
      },
      {
        "pointage_id": 2,
        "user": "Ahmed Benali",
        "site": "Site Secondaire Rabat",
        "started_at": "2024-01-15T08:30:00Z",
        "verification": {
          "is_present": false,
          "distance": 1250.8,
          "status": "far_from_site"
        }
      }
    ]
  }
}
```

## 📋 Contenu des Rapports Excel

### Rapport Global des Employés
- **Feuille 1: Résumé Employés** - Vue d'ensemble avec statistiques
- **Feuille 2: Détail par Employé** - Pointages groupés par employé
- **Feuille 3: Tous les Pointages** - Liste complète chronologique

### Rapport Individuel
- **Feuille 1: Résumé Employé** - Informations et statistiques personnelles
- **Feuille 2: Pointages Détaillés** - Tous les pointages avec coordonnées
- **Feuille 3: Statistiques Journalières** - Analyse jour par jour

### Rapport par Site
- **Feuille 1: Résumé Site** - Informations du site et statistiques
- **Feuille 2: Employés par Site** - Performance de chaque employé
- **Feuille 3: Pointages Site** - Tous les pointages sur ce site

## 🎯 Statuts de Présence

- **on_site_exact** - Position exacte (0-10m)
- **on_site_nearby** - Sur site proche (10-50m)
- **nearby_site** - Proche du site (50-100m)
- **far_from_site** - Éloigné du site (100-500m)
- **very_far_from_site** - Très éloigné (>500m)

## 🔧 Script de Test Automatique

Pour tester toutes les fonctionnalités :

```bash
php test_reports_notifications.php
```

## 📝 Notes Importantes

1. **Permissions** - Seuls les admins peuvent générer des rapports
2. **Taille des Fichiers** - Les rapports peuvent être volumineux pour de longues périodes
3. **Stockage** - Les fichiers sont stockés temporairement sur le serveur
4. **Alertes** - Les notifications sont envoyées automatiquement si `send_alert: true`
5. **Cache** - Les vérifications de présence sont mises en cache pour éviter le spam

## 🚀 Cas d'Usage Pratiques

### Génération de Rapport Mensuel
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "include_stats": true
}
```

### Surveillance en Temps Réel
```json
{
  "user_id": 2,
  "latitude": 33.5735,
  "longitude": -7.5902,
  "send_alert": true
}
```

### Audit Complet
```bash
# 1. Vérifier tous les employés
POST /api/reports/check-all-employees

# 2. Générer rapport global
POST /api/reports/employees

# 3. Télécharger le rapport
GET /api/reports/download/{filename}
```
