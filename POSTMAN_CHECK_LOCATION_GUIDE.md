# Guide Postman - Test de l'endpoint /pointage/check-location

## 🎯 Objectif
Tester l'endpoint `/pointage/check-location` qui vérifie si un utilisateur est dans la zone autorisée pour pointer.

## 📋 Prérequis

### 1. Ser<PERSON>ur d<PERSON>
```bash
php artisan serve --host=127.0.0.1 --port=8000
```

### 2. Base de données configurée
```bash
php fix_all_conflicts.php
```

## 🔐 Étape 1: Authentification

### Requête de Login
**POST** `http://127.0.0.1:8000/api/auth/login`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Body (JSON):**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Réponse attendue:**
```json
{
  "success": true,
  "message": "Connexion réussie.",
  "data": {
    "user": {
      "id": 1,
      "name": "Admin ClockIn",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "token": "YOUR_TOKEN_HERE"
  }
}
```

⚠️ **Important**: Copiez le token de la réponse pour l'utiliser dans les requêtes suivantes.

## 📍 Étape 2: Test de Vérification de Localisation

### Requête Check Location
**POST** `http://127.0.0.1:8000/api/pointage/check-location`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

### 🟢 Test 1: Position Valide (Dans la Zone)
**Body (JSON):**
```json
{
  "latitude": 33.5731,
  "longitude": -7.5898,
  "accuracy": 10.0,
  "timestamp": 1705401600
}
```

**Réponse attendue:**
```json
{
  "success": true,
  "within_range": true,
  "distance": 15.25,
  "site": "Site Principal",
  "can_pointe": true
}
```

### 🟡 Test 2: Position Proche (Limite de Zone)
**Body (JSON):**
```json
{
  "latitude": 33.5741,
  "longitude": -7.5908,
  "accuracy": 15.0,
  "timestamp": 1705401600
}
```

**Réponse attendue:**
```json
{
  "success": true,
  "within_range": true,
  "distance": 45.80,
  "site": "Site Principal",
  "can_pointe": true
}
```

### 🔴 Test 3: Position Éloignée (Hors Zone)
**Body (JSON):**
```json
{
  "latitude": 33.6731,
  "longitude": -7.6898,
  "accuracy": 8.0,
  "timestamp": 1705401600
}
```

**Réponse attendue:**
```json
{
  "success": true,
  "within_range": false,
  "distance": 12547.32,
  "site": "Site Principal",
  "can_pointe": false
}
```

### 🔴 Test 4: Position Très Éloignée (Rabat)
**Body (JSON):**
```json
{
  "latitude": 34.0209,
  "longitude": -6.8416,
  "accuracy": 12.0,
  "timestamp": 1705401600
}
```

**Réponse attendue:**
```json
{
  "success": true,
  "within_range": false,
  "distance": 85432.15,
  "site": "Site Principal",
  "can_pointe": false
}
```

## ❌ Tests d'Erreurs

### Test 5: Latitude Manquante
**Body (JSON):**
```json
{
  "longitude": -7.5898,
  "accuracy": 10.0
}
```

**Réponse attendue:**
```json
{
  "success": false,
  "message": "Latitude et longitude sont requises.",
  "message_ar": "خطوط الطول والعرض مطلوبة."
}
```

### Test 6: Coordonnées Invalides
**Body (JSON):**
```json
{
  "latitude": 91.0,
  "longitude": 181.0
}
```

**Réponse attendue:**
```json
{
  "success": false,
  "message": "Validation échouée",
  "errors": {
    "latitude": ["La latitude doit être comprise entre -90 et 90 degrés."],
    "longitude": ["La longitude doit être comprise entre -180 et 180 degrés."]
  }
}
```

### Test 7: Token Manquant
**Headers (sans Authorization):**
```
Content-Type: application/json
Accept: application/json
```

**Réponse attendue:**
```json
{
  "success": false,
  "message": "Token d'authentification requis.",
  "message_ar": "رمز المصادقة مطلوب."
}
```

## 📊 Paramètres Optionnels

### Paramètres Complets
```json
{
  "latitude": 33.5731,
  "longitude": -7.5898,
  "accuracy": 10.0,
  "timestamp": 1705401600,
  "speed": 0.0,
  "altitude": 50.0,
  "heading": 180.0
}
```

### Description des Paramètres
- **latitude** (requis): Latitude GPS (-90 à 90)
- **longitude** (requis): Longitude GPS (-180 à 180)
- **accuracy** (optionnel): Précision GPS en mètres
- **timestamp** (optionnel): Timestamp Unix de la position
- **speed** (optionnel): Vitesse en m/s
- **altitude** (optionnel): Altitude en mètres
- **heading** (optionnel): Direction en degrés

## 🔧 Script de Test Automatique

Pour tester automatiquement tous les scénarios :

```bash
php test_check_location.php
```

## 📝 Notes Importantes

1. **Distance Maximale**: 50 mètres par défaut
2. **Assignation Requise**: L'utilisateur doit être assigné à un site
3. **Token Valide**: Le token expire après 24h
4. **Coordonnées Réalistes**: Évitez 0,0 ou des coordonnées impossibles
5. **Précision GPS**: Une précision > 100m génère un avertissement

## 🎯 Cas d'Usage Réels

### Employé Arrivant au Site
```json
{
  "latitude": 33.5731,
  "longitude": -7.5898,
  "accuracy": 8.0
}
```

### Employé en Déplacement
```json
{
  "latitude": 33.5750,
  "longitude": -7.5920,
  "accuracy": 15.0,
  "speed": 1.2
}
```

### Vérification Périodique
```json
{
  "latitude": 33.5735,
  "longitude": -7.5902,
  "accuracy": 12.0,
  "timestamp": 1705401600
}
```
