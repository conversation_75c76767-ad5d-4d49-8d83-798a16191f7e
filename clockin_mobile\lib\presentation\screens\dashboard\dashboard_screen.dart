import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/auth_repository.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _isCheckedIn = false;
  DateTime? _checkinTime;
  DateTime? _checkoutTime;
  Duration _workingHours = Duration.zero;

  @override
  void initState() {
    super.initState();
    _loadTodayAttendance();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outlined),
                    SizedBox(width: 8),
                    Text('Profile'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout_outlined),
                    SizedBox(width: 8),
                    Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<AuthRepository>(
        builder: (context, authRepo, child) {
          return LoadingOverlay(
            isLoading: authRepo.isLoading,
            child: RefreshIndicator(
              onRefresh: _refreshData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConstants.spacingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Header
                    _buildWelcomeHeader(authRepo),
                    
                    const SizedBox(height: AppConstants.spacingL),
                    
                    // Today's Status Card
                    _buildTodayStatusCard(),
                    
                    const SizedBox(height: AppConstants.spacingL),
                    
                    // Check-in/Check-out Button
                    _buildAttendanceButton(),
                    
                    const SizedBox(height: AppConstants.spacingL),
                    
                    // Quick Stats
                    _buildQuickStats(),
                    
                    const SizedBox(height: AppConstants.spacingL),
                    
                    // Recent Activity
                    _buildRecentActivity(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildWelcomeHeader(AuthRepository authRepo) {
    final user = authRepo.currentUser;
    final now = DateTime.now();
    final timeOfDay = _getTimeOfDay(now);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppColors.primaryBlue,
              child: Text(
                user?.name.substring(0, 1).toUpperCase() ?? 'U',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.white,
                ),
              ),
            ),
            
            const SizedBox(width: AppConstants.spacingM),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$timeOfDay, ${user?.name ?? 'User'}!',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.spacingXS),
                  
                  Text(
                    DateFormat('EEEE, MMMM d, yyyy').format(now),
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Today\'s Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'Check-in',
                    _checkinTime != null
                        ? DateFormat('HH:mm').format(_checkinTime!)
                        : '--:--',
                    Icons.login,
                    AppColors.success,
                  ),
                ),
                
                const SizedBox(width: AppConstants.spacingM),
                
                Expanded(
                  child: _buildStatusItem(
                    'Check-out',
                    _checkoutTime != null
                        ? DateFormat('HH:mm').format(_checkoutTime!)
                        : '--:--',
                    Icons.logout,
                    AppColors.error,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            _buildStatusItem(
              'Working Hours',
              _formatDuration(_workingHours),
              Icons.access_time,
              AppColors.primaryBlue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingS),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: AppConstants.spacingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.grey600,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceButton() {
    return _isCheckedIn
        ? ErrorButton(
            text: 'Check Out',
            icon: Icons.logout,
            onPressed: _handleCheckOut,
          )
        : SuccessButton(
            text: 'Check In',
            icon: Icons.login,
            onPressed: _handleCheckIn,
          );
  }

  Widget _buildQuickStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'This Week',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard('Days Present', '4', Icons.check_circle),
                ),
                const SizedBox(width: AppConstants.spacingS),
                Expanded(
                  child: _buildStatCard('Total Hours', '32h', Icons.access_time),
                ),
                const SizedBox(width: AppConstants.spacingS),
                Expanded(
                  child: _buildStatCard('On Time', '100%', Icons.schedule),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingS),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.primaryBlue),
          const SizedBox(height: AppConstants.spacingXS),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryBlue,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Activity',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to full history
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.spacingM),
            
            // Mock recent activities
            _buildActivityItem('Check-in', 'Today 09:00 AM', Icons.login, AppColors.success),
            _buildActivityItem('Check-out', 'Yesterday 06:00 PM', Icons.logout, AppColors.error),
            _buildActivityItem('Check-in', 'Yesterday 09:15 AM', Icons.login, AppColors.success),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.spacingXS),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.spacingXS),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          
          const SizedBox(width: AppConstants.spacingS),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard_outlined),
          activeIcon: Icon(Icons.dashboard),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.access_time_outlined),
          activeIcon: Icon(Icons.access_time),
          label: 'Attendance',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.bar_chart_outlined),
          activeIcon: Icon(Icons.bar_chart),
          label: 'Reports',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person_outlined),
          activeIcon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
      onTap: _handleBottomNavTap,
    );
  }

  String _getTimeOfDay(DateTime time) {
    final hour = time.hour;
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  Future<void> _loadTodayAttendance() async {
    // TODO: Load today's attendance from API
    // Mock data for now
    setState(() {
      _isCheckedIn = false;
      _checkinTime = null;
      _checkoutTime = null;
      _workingHours = Duration.zero;
    });
  }

  Future<void> _refreshData() async {
    await _loadTodayAttendance();
  }

  void _handleCheckIn() {
    // TODO: Implement check-in logic with location
    setState(() {
      _isCheckedIn = true;
      _checkinTime = DateTime.now();
    });
  }

  void _handleCheckOut() {
    // TODO: Implement check-out logic with location
    setState(() {
      _isCheckedIn = false;
      _checkoutTime = DateTime.now();
      if (_checkinTime != null) {
        _workingHours = _checkoutTime!.difference(_checkinTime!);
      }
    });
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'profile':
        // TODO: Navigate to profile
        break;
      case 'settings':
        // TODO: Navigate to settings
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<AuthRepository>(context, listen: false).logout();
              Navigator.of(context).pushReplacementNamed('/login');
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _handleBottomNavTap(int index) {
    // TODO: Handle bottom navigation
    switch (index) {
      case 0:
        // Dashboard - already here
        break;
      case 1:
        // Attendance
        break;
      case 2:
        // Reports
        break;
      case 3:
        // Profile
        break;
    }
  }
}
