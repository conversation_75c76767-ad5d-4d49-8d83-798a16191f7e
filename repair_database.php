<?php

// Script de réparation de la base de données ClockIn
echo "=== Script de réparation de la base de données ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "1. Vérification de la connexion à la base de données...\n";
    DB::connection()->getPdo();
    echo "✓ Connexion réussie\n";

    echo "\n2. Vérification des tables existantes...\n";
    $tables = DB::select('SHOW TABLES');
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    echo "Tables existantes: " . implode(', ', $tableNames) . "\n";

    echo "\n3. Vérification de la table logs...\n";
    if (!in_array('logs', $tableNames)) {
        echo "Table logs manquante. Création...\n";
        
        DB::statement("
            CREATE TABLE logs (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT UNSIGNED NOT NULL,
                action VARCHAR(191) NOT NULL,
                details TEXT NULL,
                created_at TIMESTAMP NULL,
                INDEX idx_logs_user_id (user_id),
                INDEX idx_logs_action (action),
                CONSTRAINT fk_logs_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "✓ Table logs créée\n";
    } else {
        echo "✓ Table logs existe déjà\n";
    }

    echo "\n4. Mise à jour de la table des migrations...\n";
    
    // Marquer les migrations comme exécutées pour les tables existantes
    $migrationsToMark = [
        '0001_01_01_000000_create_users_table',
        '2025_06_05_055444_create_sites_table',
        '2025_06_05_055453_create_pointages_table',
        '2025_06_05_055457_create_verifications_table',
        '2025_06_05_055503_create_assignments_table',
        '2025_06_05_055507_create_logs_table',
        '2025_06_05_055513_add_role_to_users_table'
    ];
    
    foreach ($migrationsToMark as $migration) {
        $exists = DB::table('migrations')->where('migration', $migration)->exists();
        if (!$exists) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => 1
            ]);
            echo "✓ Migration marquée: $migration\n";
        }
    }

    echo "\n5. Test de la table logs...\n";
    
    // Test d'insertion
    $testUserId = DB::table('users')->first()->id ?? 1;
    
    DB::table('logs')->insert([
        'user_id' => $testUserId,
        'action' => 'test_repair',
        'details' => 'Test de réparation de la base de données',
        'created_at' => now()
    ]);
    
    echo "✓ Test d'insertion réussi\n";
    
    // Nettoyer le test
    DB::table('logs')->where('action', 'test_repair')->delete();
    echo "✓ Test nettoyé\n";

    echo "\n=== Réparation terminée avec succès ===\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
