<?php

// Script de configuration du système de surveillance des employés
echo "=== Configuration du Système de Surveillance des Employés ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "1. Vérification de la connexion...\n";
    DB::connection()->getPdo();
    echo "✅ Connexion réussie\n";

    echo "\n2. Vérification des tables nécessaires...\n";
    
    $requiredTables = ['users', 'sites', 'assignments', 'pointages', 'verifications', 'logs'];
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        if (Schema::hasTable($table)) {
            echo "✅ Table '$table' existe\n";
        } else {
            echo "❌ Table '$table' manquante\n";
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "\n⚠️  Tables manquantes détectées. Exécutez d'abord:\n";
        echo "   php fix_all_conflicts.php\n";
        echo "   php setup_user_site_assignment.php\n";
        return;
    }

    echo "\n3. Vérification des assignations employé-chantier...\n";
    
    $assignments = DB::table('assignments')
        ->join('users', 'assignments.user_id', '=', 'users.id')
        ->join('sites', 'assignments.site_id', '=', 'sites.id')
        ->select(
            'assignments.*',
            'users.name as user_name',
            'users.role as user_role',
            'sites.name as site_name'
        )
        ->get();
    
    if ($assignments->count() === 0) {
        echo "❌ Aucune assignation trouvée\n";
        echo "   Création d'assignations de test...\n";
        
        // Créer des assignations de base
        $users = DB::table('users')->where('role', 'employee')->get();
        $sites = DB::table('sites')->get();
        
        if ($users->count() > 0 && $sites->count() > 0) {
            foreach ($users as $index => $user) {
                $siteIndex = $index % $sites->count();
                $site = $sites[$siteIndex];
                
                DB::table('assignments')->insert([
                    'user_id' => $user->id,
                    'site_id' => $site->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                echo "   ✅ {$user->name} assigné à {$site->name}\n";
            }
        }
        
        // Recharger les assignations
        $assignments = DB::table('assignments')
            ->join('users', 'assignments.user_id', '=', 'users.id')
            ->join('sites', 'assignments.site_id', '=', 'sites.id')
            ->select(
                'assignments.*',
                'users.name as user_name',
                'users.role as user_role',
                'sites.name as site_name'
            )
            ->get();
    }
    
    echo "✅ Assignations trouvées: {$assignments->count()}\n";
    foreach ($assignments as $assignment) {
        echo "   - {$assignment->user_name} ({$assignment->user_role}) → {$assignment->site_name}\n";
    }

    echo "\n4. Création de pointages de test pour la surveillance...\n";
    
    $activePointages = DB::table('pointages')->whereNull('fin_pointage')->count();
    
    if ($activePointages === 0) {
        echo "Aucun pointage actif trouvé. Création de pointages de test...\n";
        
        foreach ($assignments->take(3) as $assignment) {
            // Récupérer les coordonnées du site
            $site = DB::table('sites')->where('id', $assignment->site_id)->first();
            
            $pointageId = DB::table('pointages')->insertGetId([
                'user_id' => $assignment->user_id,
                'site_id' => $assignment->site_id,
                'debut_pointage' => now()->subHours(2),
                'debut_latitude' => $site->latitude,
                'debut_longitude' => $site->longitude,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            echo "   ✅ Pointage créé pour {$assignment->user_name} (ID: $pointageId)\n";
        }
    } else {
        echo "✅ Pointages actifs trouvés: $activePointages\n";
    }

    echo "\n5. Test du service de surveillance...\n";
    
    try {
        // Tester la création du service
        $locationService = app(\App\Services\LocationService::class);
        $monitoringService = new \App\Services\EmployeePresenceMonitoringService($locationService);
        
        echo "✅ Service de surveillance initialisé\n";
        
        // Test avec le premier employé assigné
        if ($assignments->count() > 0) {
            $firstAssignment = $assignments->first();
            $site = DB::table('sites')->where('id', $firstAssignment->site_id)->first();
            
            echo "   Test avec {$firstAssignment->user_name}...\n";
            
            $testResult = $monitoringService->checkEmployeeOnAssignedSite(
                $firstAssignment->user_id,
                $site->latitude,
                $site->longitude
            );
            
            if ($testResult['success']) {
                echo "   ✅ Test réussi\n";
                echo "     Employé: {$testResult['employee']['name']}\n";
                echo "     Chantier: {$testResult['assigned_site']['name']}\n";
                echo "     Sur site: " . ($testResult['is_on_assigned_site'] ? 'OUI' : 'NON') . "\n";
                echo "     Distance: {$testResult['distance_from_site']}m\n";
            } else {
                echo "   ❌ Test échoué: {$testResult['message']}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Erreur lors du test du service: " . $e->getMessage() . "\n";
    }

    echo "\n6. Configuration des logs d'alertes...\n";
    
    $alertsLogPath = storage_path('logs/alerts.log');
    $alertsDir = dirname($alertsLogPath);
    
    if (!is_dir($alertsDir)) {
        mkdir($alertsDir, 0755, true);
        echo "✅ Répertoire de logs créé\n";
    }
    
    if (!file_exists($alertsLogPath)) {
        touch($alertsLogPath);
        chmod($alertsLogPath, 0644);
        echo "✅ Fichier de logs d'alertes créé\n";
    } else {
        echo "✅ Fichier de logs d'alertes existe\n";
    }

    echo "\n7. Vérification des routes de surveillance...\n";
    
    try {
        $routes = app('router')->getRoutes();
        $monitoringRoutes = 0;
        
        foreach ($routes as $route) {
            if (strpos($route->uri(), 'api/monitoring') !== false) {
                $monitoringRoutes++;
            }
        }
        
        if ($monitoringRoutes > 0) {
            echo "✅ Routes de surveillance configurées ($monitoringRoutes routes)\n";
        } else {
            echo "❌ Aucune route de surveillance trouvée\n";
        }
        
    } catch (Exception $e) {
        echo "⚠️  Impossible de vérifier les routes: " . $e->getMessage() . "\n";
    }

    echo "\n8. Test de vérification globale...\n";
    
    try {
        $globalResult = $monitoringService->checkAllActiveEmployeesOnSites();
        
        if ($globalResult['success']) {
            echo "✅ Vérification globale réussie\n";
            $summary = $globalResult['summary'];
            echo "   Total vérifié: {$summary['total_checked']}\n";
            echo "   Présents: {$summary['present_on_site']}\n";
            echo "   Absents: {$summary['absent_from_site']}\n";
            echo "   Sans assignation: {$summary['no_assigned_site']}\n";
        } else {
            echo "❌ Vérification globale échouée\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification globale: " . $e->getMessage() . "\n";
    }

    echo "\n9. Configuration des paramètres de surveillance...\n";
    
    $configPath = 'config/clockin.php';
    if (!file_exists($configPath)) {
        $configContent = "<?php\n\nreturn [\n    // Distance maximale autorisée (en mètres)\n    'max_distance' => env('CLOCKIN_MAX_DISTANCE', 50),\n    \n    // Durée de surveillance (en heures)\n    'monitoring_duration' => env('CLOCKIN_MONITORING_DURATION', 12),\n    \n    // Délai entre notifications (en minutes)\n    'notification_cooldown' => env('CLOCKIN_NOTIFICATION_COOLDOWN', 30),\n    \n    // Intervalle de vérification automatique (en minutes)\n    'auto_check_interval' => env('CLOCKIN_AUTO_CHECK_INTERVAL', 15),\n];\n";
        
        file_put_contents($configPath, $configContent);
        echo "✅ Fichier de configuration créé\n";
    } else {
        echo "✅ Fichier de configuration existe\n";
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "CONFIGURATION TERMINÉE\n";
    echo str_repeat("=", 60) . "\n";
    
    echo "\n🎯 SYSTÈME DE SURVEILLANCE PRÊT!\n";
    
    echo "\n📊 STATISTIQUES:\n";
    echo "• Employés assignés: {$assignments->count()}\n";
    echo "• Pointages actifs: " . DB::table('pointages')->whereNull('fin_pointage')->count() . "\n";
    echo "• Sites disponibles: " . DB::table('sites')->count() . "\n";
    
    echo "\n🚀 PROCHAINES ÉTAPES:\n";
    echo "1. Démarrer le serveur:\n";
    echo "   php artisan serve --host=127.0.0.1 --port=8000\n";
    echo "\n2. Tester la surveillance:\n";
    echo "   php test_employee_monitoring.php\n";
    echo "\n3. Utiliser avec Postman:\n";
    echo "   Voir POSTMAN_EMPLOYEE_MONITORING_GUIDE.md\n";
    
    echo "\n🔔 ENDPOINTS DE SURVEILLANCE:\n";
    echo "• POST /api/monitoring/check-employee-on-site\n";
    echo "• POST /api/monitoring/check-all-active\n";
    echo "• POST /api/monitoring/start-monitoring\n";
    echo "• POST /api/monitoring/stop-monitoring\n";
    echo "• GET /api/monitoring/status\n";
    
    echo "\n⚙️ FONCTIONNALITÉS DISPONIBLES:\n";
    echo "• Vérification en temps réel de présence\n";
    echo "• Notifications automatiques d'absence\n";
    echo "• Surveillance continue configurable\n";
    echo "• Gestion des employés sans assignation\n";
    echo "• Logs détaillés et alertes\n";
    echo "• Statuts de présence précis\n";

} catch (Exception $e) {
    echo "❌ Erreur lors de la configuration: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
