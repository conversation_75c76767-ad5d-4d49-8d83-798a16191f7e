import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';

class LocationService {
  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Check location permission
  Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  // Request location permission
  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  // Request location permission using permission_handler
  Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  // Get current position
  Future<Map<String, dynamic>> getCurrentPosition() async {
    try {
      // Check if location services are enabled
      final serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        return {
          'success': false,
          'message': 'Location services are disabled. Please enable location services.',
          'error': 'LOCATION_SERVICES_DISABLED',
        };
      }

      // Check location permission
      var permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        // Request permission
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          return {
            'success': false,
            'message': 'Location permission denied. Please grant location permission.',
            'error': 'LOCATION_PERMISSION_DENIED',
          };
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return {
          'success': false,
          'message': 'Location permission permanently denied. Please enable location permission in app settings.',
          'error': 'LOCATION_PERMISSION_PERMANENTLY_DENIED',
        };
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: AppConstants.locationTimeout,
      );

      // Save last known location
      await _saveLastLocation(position);

      return {
        'success': true,
        'position': position,
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'timestamp': position.timestamp,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get current location: ${e.toString()}',
        'error': 'LOCATION_ERROR',
      };
    }
  }

  // Get last known position
  Future<Map<String, dynamic>> getLastKnownPosition() async {
    try {
      final position = await Geolocator.getLastKnownPosition();
      
      if (position != null) {
        return {
          'success': true,
          'position': position,
          'latitude': position.latitude,
          'longitude': position.longitude,
          'accuracy': position.accuracy,
          'timestamp': position.timestamp,
        };
      } else {
        // Try to get from shared preferences
        final savedLocation = await _getLastLocation();
        if (savedLocation != null) {
          return {
            'success': true,
            'position': savedLocation,
            'latitude': savedLocation.latitude,
            'longitude': savedLocation.longitude,
            'accuracy': savedLocation.accuracy,
            'timestamp': savedLocation.timestamp,
          };
        }
        
        return {
          'success': false,
          'message': 'No last known position available',
          'error': 'NO_LAST_POSITION',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get last known location: ${e.toString()}',
        'error': 'LOCATION_ERROR',
      };
    }
  }

  // Calculate distance between two points in meters
  double calculateDistance(double startLatitude, double startLongitude, 
                          double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
      startLatitude, 
      startLongitude, 
      endLatitude, 
      endLongitude
    );
  }

  // Save last location to shared preferences
  Future<void> _saveLastLocation(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationData = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'speed': position.speed,
        'speedAccuracy': position.speedAccuracy,
        'heading': position.heading,
        'timestamp': position.timestamp?.millisecondsSinceEpoch,
      };
      
      await prefs.setString(AppConstants.locationKey, locationData.toString());
    } catch (e) {
      // Ignore errors when saving location
    }
  }

  // Get last location from shared preferences
  Future<Position?> _getLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString = prefs.getString(AppConstants.locationKey);
      
      if (locationString != null && locationString.isNotEmpty) {
        // Parse the location string
        // This is a simplified implementation
        return null;
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
}
