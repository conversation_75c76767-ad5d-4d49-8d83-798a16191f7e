<?php

// Script de test de l'API ClockIn
echo "=== Test de l'API ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Http\Kernel');

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

try {
    echo "1. Test de connexion à la base de données...\n";
    DB::connection()->getPdo();
    echo "✓ Connexion réussie\n";

    echo "\n2. Vérification de la table logs...\n";
    $tableExists = DB::select("SHOW TABLES LIKE 'logs'");
    if (empty($tableExists)) {
        echo "❌ Table logs manquante. Création...\n";
        
        DB::statement("
            CREATE TABLE IF NOT EXISTS logs (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT UNSIGNED NOT NULL,
                action VARCHAR(191) NOT NULL,
                details TEXT NULL,
                created_at TIMESTAMP NULL,
                INDEX idx_logs_user_id (user_id),
                INDEX idx_logs_action (action),
                CONSTRAINT fk_logs_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        echo "✓ Table logs créée\n";
    } else {
        echo "✓ Table logs existe\n";
    }

    echo "\n3. Test de l'endpoint de login...\n";
    
    // Créer une requête simulée
    $request = Request::create('/api/auth/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $request->headers->set('Content-Type', 'application/json');
    $request->headers->set('Accept', 'application/json');
    
    // Traiter la requête
    $response = $kernel->handle($request);
    
    echo "Status: " . $response->getStatusCode() . "\n";
    echo "Response: " . $response->getContent() . "\n";
    
    if ($response->getStatusCode() === 200) {
        echo "✓ Test de login réussi\n";
    } else {
        echo "❌ Test de login échoué\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
