import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/attendance_model.dart';
import '../../core/constants/app_constants.dart';
import 'auth_service.dart';

class AttendanceService {
  static const String _baseUrl = AppConstants.baseUrl;
  final AuthService _authService = AuthService();

  // Get authorization headers
  Future<Map<String, String>> _getHeaders() async {
    final token = await _authService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Check in
  Future<Map<String, dynamic>> checkIn({
    required double latitude,
    required double longitude,
    String? location,
    String? notes,
  }) async {
    try {
      final headers = await _getHeaders();
      
      final response = await http.post(
        Uri.parse('$_baseUrl/attendance/checkin'),
        headers: headers,
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return {
          'success': true,
          'attendance': AttendanceModel.fromJson(data['attendance']),
          'message': data['message'] ?? 'Check-in successful',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Check-in failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Check out
  Future<Map<String, dynamic>> checkOut({
    required double latitude,
    required double longitude,
    String? location,
    String? notes,
  }) async {
    try {
      final headers = await _getHeaders();
      
      final response = await http.post(
        Uri.parse('$_baseUrl/attendance/checkout'),
        headers: headers,
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'attendance': AttendanceModel.fromJson(data['attendance']),
          'message': data['message'] ?? 'Check-out successful',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Check-out failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Get today's attendance
  Future<Map<String, dynamic>> getTodayAttendance() async {
    try {
      final headers = await _getHeaders();
      
      final response = await http.get(
        Uri.parse('$_baseUrl/attendance/today'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'attendance': data['attendance'] != null 
              ? AttendanceModel.fromJson(data['attendance'])
              : null,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get today\'s attendance',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Get attendance history
  Future<Map<String, dynamic>> getAttendanceHistory({
    int page = 1,
    int perPage = 20,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final headers = await _getHeaders();
      
      final queryParams = <String, String>{
        'page': page.toString(),
        'per_page': perPage.toString(),
      };
      
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }
      
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }
      
      final uri = Uri.parse('$_baseUrl/attendance/history').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.get(uri, headers: headers);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final attendanceList = (data['data'] as List)
            .map((item) => AttendanceModel.fromJson(item))
            .toList();
            
        return {
          'success': true,
          'attendances': attendanceList,
          'pagination': {
            'current_page': data['current_page'],
            'last_page': data['last_page'],
            'per_page': data['per_page'],
            'total': data['total'],
          },
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get attendance history',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Get attendance statistics
  Future<Map<String, dynamic>> getAttendanceStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final headers = await _getHeaders();
      
      final queryParams = <String, String>{};
      
      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }
      
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }
      
      final uri = Uri.parse('$_baseUrl/attendance/stats').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );
      
      final response = await http.get(uri, headers: headers);
      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'stats': data['stats'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Failed to get attendance statistics',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
      };
    }
  }
}
