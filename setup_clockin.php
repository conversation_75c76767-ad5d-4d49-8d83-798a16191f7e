<?php

// Script de configuration complète ClockIn
echo "=== Configuration complète ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

function createTableIfNotExists($tableName, $createFunction) {
    if (!Schema::hasTable($tableName)) {
        echo "Création de la table $tableName...\n";
        Schema::create($tableName, $createFunction);
        echo "✓ Table $tableName créée\n";
        return true;
    } else {
        echo "✓ Table $tableName existe déjà\n";
        return false;
    }
}

try {
    echo "1. Test de connexion à la base de données...\n";
    DB::connection()->getPdo();
    echo "✓ Connexion réussie\n";

    echo "\n2. Configuration des tables principales...\n";
    
    // Table logs (priorité car elle cause l'erreur)
    createTableIfNotExists('logs', function ($table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('action');
        $table->text('details')->nullable();
        $table->timestamp('created_at')->nullable();
        $table->index('user_id');
        $table->index('action');
    });

    echo "\n3. Vérification de la colonne role dans users...\n";
    $userColumns = Schema::getColumnListing('users');
    if (!in_array('role', $userColumns)) {
        Schema::table('users', function ($table) {
            $table->string('role')->default('employee');
        });
        echo "✓ Colonne role ajoutée\n";
    } else {
        echo "✓ Colonne role existe\n";
    }

    echo "\n4. Configuration de l'utilisateur admin...\n";
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if (!$adminUser) {
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Utilisateur admin créé\n";
    } else {
        // Mettre à jour le mot de passe et le rôle si nécessaire
        DB::table('users')->where('email', '<EMAIL>')->update([
            'password' => bcrypt('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Utilisateur admin mis à jour\n";
    }

    echo "\n5. Test de l'API de login...\n";
    
    // Simuler une requête de login
    $testUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if ($testUser && password_verify('password123', $testUser->password)) {
        echo "✓ Authentification testée avec succès\n";
        
        // Test d'insertion dans logs
        $logId = DB::table('logs')->insertGetId([
            'user_id' => $testUser->id,
            'action' => 'setup_test',
            'details' => 'Test de configuration du système',
            'created_at' => now()
        ]);
        
        echo "✓ Log de test créé (ID: $logId)\n";
        
        // Nettoyer
        DB::table('logs')->where('id', $logId)->delete();
        echo "✓ Log de test nettoyé\n";
        
    } else {
        echo "❌ Problème d'authentification\n";
    }

    echo "\n6. Synchronisation des migrations...\n";
    
    $tableMigrations = [
        'users' => '0001_01_01_000000_create_users_table',
        'cache' => '0001_01_01_000001_create_cache_table',
        'sites' => '2025_06_05_055444_create_sites_table',
        'pointages' => '2025_06_05_055453_create_pointages_table',
        'verifications' => '2025_06_05_055457_create_verifications_table',
        'assignments' => '2025_06_05_055503_create_assignments_table',
        'logs' => '2025_06_05_055507_create_logs_table'
    ];
    
    $executedMigrations = DB::table('migrations')->pluck('migration')->toArray();
    $batch = max(DB::table('migrations')->max('batch') ?? 0, 1);
    
    foreach ($tableMigrations as $table => $migration) {
        if (Schema::hasTable($table) && !in_array($migration, $executedMigrations)) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => $batch
            ]);
            echo "✓ Migration marquée: $migration\n";
        }
    }

    echo "\n=== Configuration terminée avec succès ===\n";
    echo "✓ Base de données configurée\n";
    echo "✓ Table logs créée et fonctionnelle\n";
    echo "✓ Utilisateur admin prêt (<EMAIL> / password123)\n";
    echo "✓ Migrations synchronisées\n";
    echo "\nVous pouvez maintenant tester l'API:\n";
    echo "php artisan serve --host=127.0.0.1 --port=8000\n";
    echo "curl -X POST http://127.0.0.1:8000/api/auth/login -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"password123\"}'\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
    echo "\nTrace:\n" . $e->getTraceAsString() . "\n";
}
