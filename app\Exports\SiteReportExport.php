<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * Export Excel pour les rapports de site
 */
class SiteReportExport implements WithMultipleSheets
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function sheets(): array
    {
        return [
            'Résumé Site' => new SiteSummarySheet($this->data),
            'Employés par Site' => new SiteEmployeesSheet($this->data),
            'Pointages Site' => new SitePointagesSheet($this->data)
        ];
    }
}

/**
 * Feuille de résumé du site
 */
class SiteSummarySheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $site = $this->data['site'];
        $summary = $this->data['summary'];
        $period = $this->data['period'];

        return [
            ['INFORMATIONS DU SITE', ''],
            ['Nom du site', $site->name],
            ['Latitude', $site->latitude],
            ['Longitude', $site->longitude],
            ['Date de création', \Carbon\Carbon::parse($site->created_at)->format('d/m/Y')],
            ['', ''],
            ['PÉRIODE DU RAPPORT', ''],
            ['Date de début', $period['start']->format('d/m/Y')],
            ['Date de fin', $period['end']->format('d/m/Y')],
            ['', ''],
            ['STATISTIQUES GÉNÉRALES', ''],
            ['Total pointages', $summary['total_pointages']],
            ['Employés uniques', $summary['unique_employees']],
            ['Total heures travaillées', round($summary['total_hours'], 2) . ' h'],
            ['Moyenne heures/employé', $summary['unique_employees'] > 0 ? round($summary['total_hours'] / $summary['unique_employees'], 2) . ' h' : '0 h'],
            ['', ''],
            ['ACTIVITÉ PAR EMPLOYÉ', ''],
            ['Employé le plus actif', $this->getMostActiveEmployee()],
            ['Heures maximales', $this->getMaxHours() . ' h'],
            ['Pointages maximaux', $this->getMaxPointages()],
        ];
    }

    public function headings(): array
    {
        return ['Métrique', 'Valeur'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
            'A1:B1' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']]
            ],
            'A7:B7' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF'], 'bold' => true]
            ],
            'A11:B11' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFC000']
                ],
                'font' => ['bold' => true]
            ],
            'A17:B17' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E74C3C']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF'], 'bold' => true]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return ['A' => 25, 'B' => 30];
    }

    public function title(): string
    {
        return 'Résumé Site';
    }

    private function getMostActiveEmployee(): string
    {
        $employeeStats = $this->data['employee_statistics'];
        if (empty($employeeStats)) return 'Aucun';
        
        $mostActive = array_reduce($employeeStats, function($carry, $item) {
            return (!$carry || $item['pointages_count'] > $carry['pointages_count']) ? $item : $carry;
        });
        
        return $mostActive['user']->name ?? 'Inconnu';
    }

    private function getMaxHours(): float
    {
        $employeeStats = $this->data['employee_statistics'];
        if (empty($employeeStats)) return 0;
        
        return max(array_column($employeeStats, 'total_hours'));
    }

    private function getMaxPointages(): int
    {
        $employeeStats = $this->data['employee_statistics'];
        if (empty($employeeStats)) return 0;
        
        return max(array_column($employeeStats, 'pointages_count'));
    }
}

/**
 * Feuille des employés du site
 */
class SiteEmployeesSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['employee_statistics'] as $employeeData) {
            $user = $employeeData['user'];
            
            $rows[] = [
                $user->id,
                $user->name,
                $user->email,
                $user->role,
                $employeeData['pointages_count'],
                round($employeeData['total_hours'], 2),
                $employeeData['pointages_count'] > 0 ? round($employeeData['total_hours'] / $employeeData['pointages_count'], 2) : 0,
                $employeeData['first_pointage'] ? \Carbon\Carbon::parse($employeeData['first_pointage'])->format('d/m/Y H:i') : '',
                $employeeData['last_pointage'] ? \Carbon\Carbon::parse($employeeData['last_pointage'])->format('d/m/Y H:i') : '',
                $this->calculateEmployeeEfficiency($employeeData)
            ];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Nom',
            'Email',
            'Rôle',
            'Nb Pointages',
            'Total Heures',
            'Moy. Heures/Pointage',
            'Premier Pointage',
            'Dernier Pointage',
            'Efficacité'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            'A:J' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8, 'B' => 20, 'C' => 25, 'D' => 12,
            'E' => 15, 'F' => 15, 'G' => 20, 'H' => 18,
            'I' => 18, 'J' => 12
        ];
    }

    public function title(): string
    {
        return 'Employés par Site';
    }

    private function calculateEmployeeEfficiency(array $employeeData): string
    {
        $hours = $employeeData['total_hours'];
        $pointages = $employeeData['pointages_count'];
        
        if ($pointages === 0) return 'N/A';
        
        $avgHours = $hours / $pointages;
        
        if ($avgHours >= 8) return 'Excellent';
        if ($avgHours >= 6) return 'Bon';
        if ($avgHours >= 4) return 'Moyen';
        return 'Faible';
    }
}

/**
 * Feuille de tous les pointages du site
 */
class SitePointagesSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['pointages'] as $pointage) {
            $duration = '';
            $hours = 0;
            
            if ($pointage->fin_pointage) {
                $start = \Carbon\Carbon::parse($pointage->debut_pointage);
                $end = \Carbon\Carbon::parse($pointage->fin_pointage);
                $duration = $start->diff($end)->format('%H:%I:%S');
                $hours = $start->diffInHours($end);
            }
            
            $rows[] = [
                $pointage->id,
                $pointage->user->name,
                $pointage->user->email,
                \Carbon\Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                \Carbon\Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                $pointage->fin_pointage ? \Carbon\Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                $duration,
                $hours > 0 ? round($hours, 2) : '',
                $pointage->fin_pointage ? 'Terminé' : 'En cours',
                round($pointage->debut_latitude, 6),
                round($pointage->debut_longitude, 6),
                $pointage->fin_latitude ? round($pointage->fin_latitude, 6) : '',
                $pointage->fin_longitude ? round($pointage->fin_longitude, 6) : ''
            ];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID Pointage',
            'Employé',
            'Email',
            'Date',
            'Heure Début',
            'Heure Fin',
            'Durée',
            'Heures',
            'Statut',
            'Lat. Début',
            'Lon. Début',
            'Lat. Fin',
            'Lon. Fin'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            'A:M' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, 'B' => 20, 'C' => 25, 'D' => 12,
            'E' => 12, 'F' => 12, 'G' => 12, 'H' => 10,
            'I' => 12, 'J' => 15, 'K' => 15, 'L' => 15, 'M' => 15
        ];
    }

    public function title(): string
    {
        return 'Pointages Site';
    }
}
