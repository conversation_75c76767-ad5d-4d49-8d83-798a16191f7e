<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * Export Excel détaillé pour un employé individuel
 */
class DetailedPointagesExport implements WithMultipleSheets
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function sheets(): array
    {
        return [
            'Résumé Employé' => new EmployeeSummaryDetailSheet($this->data),
            'Pointages Détaillés' => new EmployeePointagesSheet($this->data),
            'Statistiques Journalières' => new DailyStatsSheet($this->data)
        ];
    }
}

/**
 * Feuille de résumé pour un employé
 */
class EmployeeSummaryDetailSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $user = $this->data['user'];
        $stats = $this->data['statistics'];
        $period = $this->data['period'];

        return [
            ['INFORMATIONS EMPLOYÉ', ''],
            ['Nom', $user->name],
            ['Email', $user->email],
            ['Rôle', $user->role],
            ['', ''],
            ['PÉRIODE DU RAPPORT', ''],
            ['Date de début', $period['start']->format('d/m/Y')],
            ['Date de fin', $period['end']->format('d/m/Y')],
            ['', ''],
            ['STATISTIQUES GÉNÉRALES', ''],
            ['Total pointages', $stats['total_pointages']],
            ['Pointages terminés', $stats['completed_pointages']],
            ['Pointages en cours', $stats['active_pointages']],
            ['Total heures travaillées', round($stats['total_hours'], 2) . ' h'],
            ['Moyenne heures/jour', $stats['average_daily_hours'] . ' h'],
            ['Nombre de sites', count($stats['sites_worked'])],
            ['Sites travaillés', implode(', ', $stats['sites_worked'])],
            ['', ''],
            ['TAUX DE PERFORMANCE', ''],
            ['Taux de complétion', $stats['completed_pointages'] > 0 ? round(($stats['completed_pointages'] / $stats['total_pointages']) * 100, 1) . '%' : '0%'],
            ['Jours travaillés', count($stats['daily_breakdown'])],
        ];
    }

    public function headings(): array
    {
        return ['Métrique', 'Valeur'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
            'A1:B1' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']]
            ],
            'A6:B6' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF'], 'bold' => true]
            ],
            'A10:B10' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFC000']
                ],
                'font' => ['bold' => true]
            ],
            'A19:B19' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E74C3C']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF'], 'bold' => true]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return ['A' => 25, 'B' => 30];
    }

    public function title(): string
    {
        return 'Résumé Employé';
    }
}

/**
 * Feuille des pointages détaillés
 */
class EmployeePointagesSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['pointages'] as $pointage) {
            $duration = '';
            $hours = 0;
            
            if ($pointage->fin_pointage) {
                $start = \Carbon\Carbon::parse($pointage->debut_pointage);
                $end = \Carbon\Carbon::parse($pointage->fin_pointage);
                $duration = $start->diff($end)->format('%H:%I:%S');
                $hours = $start->diffInHours($end);
            }
            
            $rows[] = [
                $pointage->id,
                $pointage->site->name,
                \Carbon\Carbon::parse($pointage->debut_pointage)->format('d/m/Y'),
                \Carbon\Carbon::parse($pointage->debut_pointage)->format('H:i:s'),
                $pointage->fin_pointage ? \Carbon\Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                $duration,
                $hours > 0 ? round($hours, 2) : '',
                $pointage->fin_pointage ? 'Terminé' : 'En cours',
                round($pointage->debut_latitude, 6),
                round($pointage->debut_longitude, 6),
                $pointage->fin_latitude ? round($pointage->fin_latitude, 6) : '',
                $pointage->fin_longitude ? round($pointage->fin_longitude, 6) : '',
                \Carbon\Carbon::parse($pointage->created_at)->format('d/m/Y H:i')
            ];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Site',
            'Date',
            'Heure Début',
            'Heure Fin',
            'Durée (H:M:S)',
            'Heures Décimales',
            'Statut',
            'Latitude Début',
            'Longitude Début',
            'Latitude Fin',
            'Longitude Fin',
            'Créé le'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            'A:M' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8, 'B' => 25, 'C' => 12, 'D' => 12,
            'E' => 12, 'F' => 15, 'G' => 15, 'H' => 12,
            'I' => 15, 'J' => 15, 'K' => 15, 'L' => 15, 'M' => 18
        ];
    }

    public function title(): string
    {
        return 'Pointages Détaillés';
    }
}

/**
 * Feuille des statistiques journalières
 */
class DailyStatsSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['statistics']['daily_breakdown'] as $dayData) {
            $rows[] = [
                \Carbon\Carbon::parse($dayData['date'])->format('d/m/Y'),
                \Carbon\Carbon::parse($dayData['date'])->locale('fr')->dayName,
                $dayData['pointages'],
                round($dayData['hours'], 2),
                implode(', ', $dayData['sites']),
                $dayData['hours'] >= 8 ? 'Journée complète' : ($dayData['hours'] >= 4 ? 'Demi-journée' : 'Partiel')
            ];
        }
        
        // Ajouter une ligne de total
        $totalHours = array_sum(array_column($this->data['statistics']['daily_breakdown'], 'hours'));
        $totalPointages = array_sum(array_column($this->data['statistics']['daily_breakdown'], 'pointages'));
        
        $rows[] = ['', '', '', '', '', ''];
        $rows[] = [
            'TOTAL',
            '',
            $totalPointages,
            round($totalHours, 2),
            '',
            ''
        ];
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Jour',
            'Nb Pointages',
            'Heures Travaillées',
            'Sites',
            'Type de Journée'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $lastRow = count($this->data['statistics']['daily_breakdown']) + 2;
        
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']]
            ],
            "A{$lastRow}:F{$lastRow}" => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFC000']
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, 'B' => 12, 'C' => 15,
            'D' => 18, 'E' => 30, 'F' => 18
        ];
    }

    public function title(): string
    {
        return 'Statistiques Journalières';
    }
}
