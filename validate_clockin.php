<?php

// Script de validation complète ClockIn
echo "=== Validation complète du projet ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Http\Kernel');

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

$errors = [];
$warnings = [];
$success = [];

function addError($message) {
    global $errors;
    $errors[] = $message;
    echo "❌ $message\n";
}

function addWarning($message) {
    global $warnings;
    $warnings[] = $message;
    echo "⚠️  $message\n";
}

function addSuccess($message) {
    global $success;
    $success[] = $message;
    echo "✅ $message\n";
}

try {
    echo "\n1. Test de connexion à la base de données...\n";
    DB::connection()->getPdo();
    addSuccess("Connexion à la base de données réussie");

    echo "\n2. Vérification des tables requises...\n";
    $requiredTables = ['users', 'sites', 'pointages', 'verifications', 'assignments', 'logs'];
    
    foreach ($requiredTables as $table) {
        if (Schema::hasTable($table)) {
            addSuccess("Table '$table' existe");
        } else {
            addError("Table '$table' manquante");
        }
    }

    echo "\n3. Vérification de la structure de la table logs...\n";
    if (Schema::hasTable('logs')) {
        $columns = Schema::getColumnListing('logs');
        $requiredColumns = ['id', 'user_id', 'action', 'details', 'created_at'];
        
        foreach ($requiredColumns as $column) {
            if (in_array($column, $columns)) {
                addSuccess("Colonne logs.$column existe");
            } else {
                addError("Colonne logs.$column manquante");
            }
        }
    }

    echo "\n4. Vérification de l'utilisateur admin...\n";
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if ($adminUser) {
        addSuccess("Utilisateur admin existe");
        
        if (password_verify('password123', $adminUser->password)) {
            addSuccess("Mot de passe admin correct");
        } else {
            addWarning("Mot de passe admin incorrect - mise à jour nécessaire");
        }
        
        if (isset($adminUser->role) && $adminUser->role === 'admin') {
            addSuccess("Rôle admin configuré");
        } else {
            addWarning("Rôle admin manquant ou incorrect");
        }
    } else {
        addError("Utilisateur admin manquant");
    }

    echo "\n5. Test de l'API de login...\n";
    
    // Créer une requête simulée
    $request = Request::create('/api/auth/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $request->headers->set('Content-Type', 'application/json');
    $request->headers->set('Accept', 'application/json');
    
    try {
        $response = $kernel->handle($request);
        $statusCode = $response->getStatusCode();
        $content = $response->getContent();
        
        echo "Status Code: $statusCode\n";
        
        if ($statusCode === 200) {
            addSuccess("API de login fonctionne");
            
            $responseData = json_decode($content, true);
            if (isset($responseData['success']) && $responseData['success'] === true) {
                addSuccess("Réponse de login valide");
                
                if (isset($responseData['data']['token'])) {
                    addSuccess("Token généré correctement");
                } else {
                    addWarning("Token manquant dans la réponse");
                }
                
                if (isset($responseData['data']['user'])) {
                    addSuccess("Données utilisateur retournées");
                } else {
                    addWarning("Données utilisateur manquantes");
                }
            } else {
                addError("Réponse de login invalide");
            }
        } else {
            addError("API de login échoue (Status: $statusCode)");
            echo "Réponse: $content\n";
        }
        
    } catch (Exception $e) {
        addError("Erreur lors du test API: " . $e->getMessage());
    }

    echo "\n6. Test d'insertion dans la table logs...\n";
    
    if ($adminUser) {
        try {
            $logId = DB::table('logs')->insertGetId([
                'user_id' => $adminUser->id,
                'action' => 'validation_test',
                'details' => 'Test de validation du système',
                'created_at' => now()
            ]);
            
            addSuccess("Insertion dans logs réussie (ID: $logId)");
            
            // Nettoyer
            DB::table('logs')->where('id', $logId)->delete();
            addSuccess("Nettoyage du test réussi");
            
        } catch (Exception $e) {
            addError("Erreur lors de l'insertion dans logs: " . $e->getMessage());
        }
    }

    echo "\n7. Vérification des fichiers de configuration...\n";
    
    $configFiles = [
        '.env' => 'Configuration environnement',
        'config/database.php' => 'Configuration base de données',
        'routes/api.php' => 'Routes API'
    ];
    
    foreach ($configFiles as $file => $description) {
        if (file_exists($file)) {
            addSuccess("$description existe");
        } else {
            addError("$description manquant: $file");
        }
    }

} catch (Exception $e) {
    addError("Erreur critique: " . $e->getMessage());
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "RÉSUMÉ DE LA VALIDATION\n";
echo str_repeat("=", 60) . "\n";

echo "\n✅ SUCCÈS (" . count($success) . "):\n";
foreach ($success as $msg) {
    echo "  • $msg\n";
}

if (!empty($warnings)) {
    echo "\n⚠️  AVERTISSEMENTS (" . count($warnings) . "):\n";
    foreach ($warnings as $msg) {
        echo "  • $msg\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ ERREURS (" . count($errors) . "):\n";
    foreach ($errors as $msg) {
        echo "  • $msg\n";
    }
    echo "\n🔧 ACTIONS RECOMMANDÉES:\n";
    echo "  • Exécuter: php setup_clockin.php\n";
    echo "  • Vérifier la configuration de la base de données\n";
    echo "  • Relancer la validation\n";
} else {
    echo "\n🎉 VALIDATION RÉUSSIE!\n";
    echo "Le projet ClockIn est prêt à l'utilisation.\n";
    echo "\n🚀 POUR DÉMARRER:\n";
    echo "  • php artisan serve --host=127.0.0.1 --port=8000\n";
    echo "  • Accéder à: http://127.0.0.1:8000/docs\n";
    echo "  • Tester avec: <EMAIL> / password123\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
