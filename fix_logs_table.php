<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// Configuration de la base de données
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'clockin_db',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Vérifier si la table logs existe
    $tableExists = Capsule::schema()->hasTable('logs');
    
    if (!$tableExists) {
        echo "Création de la table logs...\n";
        
        Capsule::schema()->create('logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('action');
            $table->text('details')->nullable();
            $table->timestamp('created_at')->nullable();
            
            $table->index('user_id');
            $table->index('action');
            
            // Clé étrangère
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
        
        echo "Table logs créée avec succès!\n";
    } else {
        echo "La table logs existe déjà.\n";
    }
    
    // Vérifier la structure
    $columns = Capsule::schema()->getColumnListing('logs');
    echo "Colonnes de la table logs: " . implode(', ', $columns) . "\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
