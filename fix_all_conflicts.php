<?php

// Script de réparation automatique de tous les conflits ClockIn
echo "=== Réparation automatique des conflits ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

$fixes = [];

function logFix($message) {
    global $fixes;
    $fixes[] = $message;
    echo "🔧 $message\n";
}

try {
    echo "1. Vérification de la connexion...\n";
    DB::connection()->getPdo();
    echo "✅ Connexion réussie\n";

    echo "\n2. Création/Vérification de la table logs...\n";
    if (!Schema::hasTable('logs')) {
        Schema::create('logs', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->text('details')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->index('user_id');
            $table->index('action');
        });
        logFix("Table logs créée");
    } else {
        echo "✅ Table logs existe\n";
    }

    echo "\n3. Vérification/Ajout des colonnes manquantes dans users...\n";
    $userColumns = Schema::getColumnListing('users');

    // Vérifier et ajouter la colonne role
    if (!in_array('role', $userColumns)) {
        Schema::table('users', function ($table) {
            $table->string('role')->default('employee');
        });
        logFix("Colonne role ajoutée à la table users");
        $userColumns[] = 'role'; // Mettre à jour la liste
    } else {
        echo "✅ Colonne role existe\n";
    }

    // Vérifier et ajouter la colonne email_verified_at
    if (!in_array('email_verified_at', $userColumns)) {
        Schema::table('users', function ($table) {
            $table->timestamp('email_verified_at')->nullable();
        });
        logFix("Colonne email_verified_at ajoutée à la table users");
        $userColumns[] = 'email_verified_at'; // Mettre à jour la liste
    } else {
        echo "✅ Colonne email_verified_at existe\n";
    }

    // Vérifier et ajouter la colonne remember_token
    if (!in_array('remember_token', $userColumns)) {
        Schema::table('users', function ($table) {
            $table->string('remember_token', 100)->nullable();
        });
        logFix("Colonne remember_token ajoutée à la table users");
    } else {
        echo "✅ Colonne remember_token existe\n";
    }

    echo "\n4. Configuration de l'utilisateur admin...\n";
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();

    if (!$adminUser) {
        // Préparer les données d'insertion en fonction des colonnes disponibles
        $insertData = [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Ajouter les colonnes optionnelles si elles existent
        if (in_array('role', $userColumns)) {
            $insertData['role'] = 'admin';
        }
        if (in_array('email_verified_at', $userColumns)) {
            $insertData['email_verified_at'] = now();
        }

        DB::table('users')->insert($insertData);
        logFix("Utilisateur admin créé");
    } else {
        // Vérifier et corriger le mot de passe et le rôle
        $needsUpdate = false;
        $updates = [];

        if (!password_verify('password123', $adminUser->password)) {
            $updates['password'] = bcrypt('password123');
            $needsUpdate = true;
        }

        // Vérifier le rôle seulement si la colonne existe
        if (in_array('role', $userColumns)) {
            if (!isset($adminUser->role) || $adminUser->role !== 'admin') {
                $updates['role'] = 'admin';
                $needsUpdate = true;
            }
        }

        // Vérifier email_verified_at seulement si la colonne existe
        if (in_array('email_verified_at', $userColumns)) {
            if (!isset($adminUser->email_verified_at) || !$adminUser->email_verified_at) {
                $updates['email_verified_at'] = now();
                $needsUpdate = true;
            }
        }

        if ($needsUpdate) {
            $updates['updated_at'] = now();
            DB::table('users')->where('id', $adminUser->id)->update($updates);
            logFix("Utilisateur admin mis à jour");
        } else {
            echo "✅ Utilisateur admin correct\n";
        }
    }

    echo "\n5. Synchronisation des migrations...\n";
    $tableMigrations = [
        'users' => '0001_01_01_000000_create_users_table',
        'cache' => '0001_01_01_000001_create_cache_table',
        'sites' => '2025_06_05_055444_create_sites_table',
        'pointages' => '2025_06_05_055453_create_pointages_table',
        'verifications' => '2025_06_05_055457_create_verifications_table',
        'assignments' => '2025_06_05_055503_create_assignments_table',
        'logs' => '2025_06_05_055507_create_logs_table'
    ];
    
    $executedMigrations = DB::table('migrations')->pluck('migration')->toArray();
    $batch = max(DB::table('migrations')->max('batch') ?? 0, 1);
    
    foreach ($tableMigrations as $table => $migration) {
        if (Schema::hasTable($table) && !in_array($migration, $executedMigrations)) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => $batch
            ]);
            logFix("Migration marquée: $migration");
        }
    }
    
    // Marquer la migration role si la colonne existe
    if (in_array('role', $userColumns) && !in_array('2025_06_05_055513_add_role_to_users_table', $executedMigrations)) {
        DB::table('migrations')->insert([
            'migration' => '2025_06_05_055513_add_role_to_users_table',
            'batch' => $batch
        ]);
        logFix("Migration role marquée");
    }

    echo "\n6. Test de fonctionnement de la table logs...\n";
    $testUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if ($testUser) {
        try {
            $logId = DB::table('logs')->insertGetId([
                'user_id' => $testUser->id,
                'action' => 'system_repair',
                'details' => 'Test de réparation automatique du système',
                'created_at' => now()
            ]);
            
            echo "✅ Test d'insertion logs réussi (ID: $logId)\n";
            
            // Nettoyer
            DB::table('logs')->where('id', $logId)->delete();
            echo "✅ Nettoyage réussi\n";
            
        } catch (Exception $e) {
            logFix("Erreur lors du test logs: " . $e->getMessage());
        }
    }

    echo "\n7. Vérification des sites de test...\n";
    $siteCount = DB::table('sites')->count();
    
    if ($siteCount === 0) {
        DB::table('sites')->insert([
            [
                'name' => 'Site Principal',
                'latitude' => 33.5731,
                'longitude' => -7.5898,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Site Secondaire',
                'latitude' => 33.5831,
                'longitude' => -7.5998,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
        logFix("Sites de test créés");
    } else {
        echo "✅ Sites existent ($siteCount sites)\n";
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "RÉPARATION TERMINÉE\n";
    echo str_repeat("=", 60) . "\n";
    
    if (!empty($fixes)) {
        echo "\n🔧 CORRECTIONS APPLIQUÉES:\n";
        foreach ($fixes as $fix) {
            echo "  • $fix\n";
        }
    }
    
    echo "\n✅ SYSTÈME PRÊT!\n";
    echo "\n🚀 PROCHAINES ÉTAPES:\n";
    echo "  1. Démarrer le serveur: php artisan serve --host=127.0.0.1 --port=8000\n";
    echo "  2. Tester l'API: php test_login_api.php\n";
    echo "  3. Valider le système: php validate_clockin.php\n";
    echo "  4. Accéder à la documentation: http://127.0.0.1:8000/docs\n";
    
    echo "\n📋 IDENTIFIANTS DE TEST:\n";
    echo "  Email: <EMAIL>\n";
    echo "  Mot de passe: password123\n";

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
