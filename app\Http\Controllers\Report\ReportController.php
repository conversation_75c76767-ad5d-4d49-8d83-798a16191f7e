<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiResponseTrait;
use App\Services\ExportService;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * @group Reports & Exports
 *
 * APIs pour la génération de rapports et exports Excel
 */
class ReportController extends Controller
{
    use ApiResponseTrait;

    private ExportService $exportService;
    private NotificationService $notificationService;

    public function __construct(
        ExportService $exportService,
        NotificationService $notificationService
    ) {
        $this->exportService = $exportService;
        $this->notificationService = $notificationService;
    }

    /**
     * Générer un rapport Excel global des employés
     *
     * @authenticated
     * @bodyParam start_date string required Date de début (YYYY-MM-DD). Example: 2024-01-01
     * @bodyParam end_date string required Date de fin (YYYY-MM-DD). Example: 2024-01-31
     * @bodyParam include_stats boolean optional Inclure les statistiques détaillées. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Rapport généré avec succès",
     *   "data": {
     *     "filename": "rapport_employes_2024-01-01_2024-01-31.xlsx",
     *     "download_url": "/api/reports/download/rapport_employes_2024-01-01_2024-01-31.xlsx",
     *     "file_size": "245 KB",
     *     "generated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function generateEmployeeReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'include_stats' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            $options = [
                'include_stats' => $request->boolean('include_stats', true)
            ];

            $filePath = $this->exportService->generateEmployeeReport($startDate, $endDate, $options);
            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'period' => [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]
            ], 'Rapport généré avec succès.', 'تم إنشاء التقرير بنجاح.');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la génération du rapport.',
                'خطأ أثناء إنشاء التقرير.',
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport Excel pour un employé spécifique
     *
     * @authenticated
     * @urlParam user_id integer required ID de l'employé. Example: 1
     * @bodyParam start_date string required Date de début. Example: 2024-01-01
     * @bodyParam end_date string required Date de fin. Example: 2024-01-31
     */
    public function generateIndividualReport(Request $request, int $userId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            $filePath = $this->exportService->generateIndividualEmployeeReport($userId, $startDate, $endDate);
            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'user_id' => $userId
            ], 'Rapport individuel généré avec succès.', 'تم إنشاء التقرير الفردي بنجاح.');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la génération du rapport.',
                'خطأ أثناء إنشاء التقرير.',
                500,
                $e
            );
        }
    }

    /**
     * Générer un rapport Excel par site
     *
     * @authenticated
     * @urlParam site_id integer required ID du site. Example: 1
     * @bodyParam start_date string required Date de début. Example: 2024-01-01
     * @bodyParam end_date string required Date de fin. Example: 2024-01-31
     */
    public function generateSiteReport(Request $request, int $siteId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();

            $filePath = $this->exportService->generateSiteReport($siteId, $startDate, $endDate);
            $filename = basename($filePath);
            $fileSize = $this->formatFileSize(filesize($filePath));

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => "/api/reports/download/{$filename}",
                'file_size' => $fileSize,
                'generated_at' => now()->toISOString(),
                'site_id' => $siteId
            ], 'Rapport de site généré avec succès.', 'تم إنشاء تقرير الموقع بنجاح.');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la génération du rapport.',
                'خطأ أثناء إنشاء التقرير.',
                500,
                $e
            );
        }
    }

    /**
     * Télécharger un fichier de rapport
     *
     * @authenticated
     * @urlParam filename string required Nom du fichier à télécharger. Example: rapport_employes_2024-01-01_2024-01-31.xlsx
     */
    public function downloadReport(string $filename): BinaryFileResponse
    {
        $filePath = storage_path('app/' . $filename);

        if (!file_exists($filePath)) {
            abort(404, 'Fichier non trouvé');
        }

        // Vérifier que le fichier appartient bien aux rapports (sécurité)
        if (!str_contains($filename, 'rapport_')) {
            abort(403, 'Accès non autorisé');
        }

        return response()->download($filePath, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]);
    }

    /**
     * Vérifier la présence d'un employé sur site
     *
     * @authenticated
     * @bodyParam user_id integer required ID de l'employé à vérifier. Example: 1
     * @bodyParam latitude float required Latitude de la position. Example: 33.5731
     * @bodyParam longitude float required Longitude de la position. Example: -7.5898
     * @bodyParam send_alert boolean optional Envoyer une alerte si absent. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "is_present": true,
     *     "distance": 15.25,
     *     "max_distance": 50,
     *     "site": {
     *       "id": 1,
     *       "name": "Site Principal",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     },
     *     "status": "on_site_nearby",
     *     "verification_time": "2024-01-15T10:30:00Z",
     *     "message": "Employé présent sur site (à 15.25m)"
     *   }
     * }
     */
    public function verifyEmployeePresence(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'send_alert' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Données de validation invalides.',
                'بيانات التحقق غير صالحة.',
                422,
                null,
                $validator->errors()
            );
        }

        try {
            $user = \App\Models\User::findOrFail($request->user_id);
            
            $verificationResult = $this->notificationService->verifyEmployeePresence(
                $user,
                $request->latitude,
                $request->longitude
            );

            // Envoyer une alerte si demandé et si l'employé n'est pas présent
            if ($request->boolean('send_alert', false) && !$verificationResult['is_present']) {
                $this->notificationService->sendPresenceAlert($user, $verificationResult);
            }

            return $this->successResponse(
                $verificationResult,
                'Vérification de présence effectuée.',
                'تم التحقق من الحضور.'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la vérification.',
                'خطأ أثناء التحقق.',
                500,
                $e
            );
        }
    }

    /**
     * Vérifier tous les employés actifs
     *
     * @authenticated
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "total_checked": 5,
     *     "present_count": 4,
     *     "absent_count": 1,
     *     "verifications": [...]
     *   }
     * }
     */
    public function checkAllActiveEmployees(): JsonResponse
    {
        try {
            $results = $this->notificationService->checkAllActiveEmployees();
            
            $summary = [
                'total_checked' => count($results),
                'present_count' => count(array_filter($results, fn($r) => $r['verification']['is_present'])),
                'absent_count' => count(array_filter($results, fn($r) => !$r['verification']['is_present'])),
                'verifications' => $results
            ];

            return $this->successResponse(
                $summary,
                'Vérification globale effectuée.',
                'تم التحقق الشامل.'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Erreur lors de la vérification globale.',
                'خطأ أثناء التحقق الشامل.',
                500,
                $e
            );
        }
    }

    /**
     * Formate la taille d'un fichier
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
