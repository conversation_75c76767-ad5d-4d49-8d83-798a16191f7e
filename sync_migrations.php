<?php

// Script de synchronisation des migrations
echo "=== Synchronisation des migrations ClockIn ===\n";

// Charger Laravel
require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "1. Vérification de la connexion...\n";
    DB::connection()->getPdo();
    echo "✓ Connexion réussie\n";

    echo "\n2. Vérification des tables existantes...\n";
    $tables = DB::select('SHOW TABLES');
    $tableNames = array_map(function($table) {
        return array_values((array)$table)[0];
    }, $tables);
    
    echo "Tables existantes: " . implode(', ', $tableNames) . "\n";

    echo "\n3. Vérification des migrations exécutées...\n";
    $executedMigrations = DB::table('migrations')->pluck('migration')->toArray();
    echo "Migrations exécutées: " . count($executedMigrations) . "\n";

    echo "\n4. Synchronisation des migrations...\n";
    
    // Mapping table -> migration
    $tableMigrations = [
        'users' => '0001_01_01_000000_create_users_table',
        'cache' => '0001_01_01_000001_create_cache_table',
        'jobs' => '0001_01_01_000002_create_jobs_table',
        'sites' => '2025_06_05_055444_create_sites_table',
        'pointages' => '2025_06_05_055453_create_pointages_table',
        'verifications' => '2025_06_05_055457_create_verifications_table',
        'assignments' => '2025_06_05_055503_create_assignments_table',
        'logs' => '2025_06_05_055507_create_logs_table'
    ];
    
    $batch = 1;
    $migrationsAdded = 0;
    
    foreach ($tableMigrations as $table => $migration) {
        if (in_array($table, $tableNames) && !in_array($migration, $executedMigrations)) {
            DB::table('migrations')->insert([
                'migration' => $migration,
                'batch' => $batch
            ]);
            echo "✓ Migration marquée: $migration\n";
            $migrationsAdded++;
        }
    }
    
    // Créer la table logs si elle n'existe pas
    if (!in_array('logs', $tableNames)) {
        echo "\n5. Création de la table logs...\n";
        
        try {
            Schema::create('logs', function ($table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('action');
                $table->text('details')->nullable();
                $table->timestamp('created_at')->nullable();
                $table->index('user_id');
                $table->index('action');
            });
            echo "✓ Table logs créée\n";
            
            // Marquer la migration comme exécutée
            if (!in_array('2025_06_05_055507_create_logs_table', $executedMigrations)) {
                DB::table('migrations')->insert([
                    'migration' => '2025_06_05_055507_create_logs_table',
                    'batch' => $batch
                ]);
                echo "✓ Migration logs marquée\n";
                $migrationsAdded++;
            }
            
        } catch (Exception $e) {
            echo "❌ Erreur lors de la création de logs: " . $e->getMessage() . "\n";
        }
    }
    
    // Ajouter la colonne role aux users si elle n'existe pas
    echo "\n6. Vérification de la colonne role dans users...\n";
    $userColumns = Schema::getColumnListing('users');
    
    if (!in_array('role', $userColumns)) {
        echo "Ajout de la colonne role...\n";
        Schema::table('users', function ($table) {
            $table->string('role')->default('employee');
        });
        echo "✓ Colonne role ajoutée\n";
        
        // Marquer la migration
        if (!in_array('2025_06_05_055513_add_role_to_users_table', $executedMigrations)) {
            DB::table('migrations')->insert([
                'migration' => '2025_06_05_055513_add_role_to_users_table',
                'batch' => $batch
            ]);
            echo "✓ Migration role marquée\n";
            $migrationsAdded++;
        }
    } else {
        echo "✓ Colonne role existe déjà\n";
    }

    echo "\n7. Vérification de l'utilisateur admin...\n";
    $adminUser = DB::table('users')->where('email', '<EMAIL>')->first();
    
    if (!$adminUser) {
        echo "Création de l'utilisateur admin...\n";
        DB::table('users')->insert([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Utilisateur admin créé\n";
    } else {
        echo "✓ Utilisateur admin existe\n";
    }

    echo "\n=== Synchronisation terminée ===\n";
    echo "Migrations ajoutées: $migrationsAdded\n";
    echo "Base de données prête pour l'utilisation\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
