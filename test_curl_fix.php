<?php

// Test rapide pour vérifier la correction du Content-Type
echo "=== Test de correction Content-Type ===\n";

$baseUrl = 'http://127.0.0.1:8000';
$endpoint = '/api/pointage/check-location';
$token = '****************************************';

$testData = [
    'latitude' => 33.5731,
    'longitude' => -7.5898,
    'accuracy' => 10.0,
    'timestamp' => 1705401600
];

echo "1. Test avec Content-Type: text/plain (INCORRECT)...\n";

$curl1 = curl_init();
curl_setopt_array($curl1, [
    CURLOPT_URL => $baseUrl . $endpoint,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: text/plain',  // INCORRECT
        'Authorization: Bearer ' . $token
    ],
]);

$response1 = curl_exec($curl1);
$httpCode1 = curl_getinfo($curl1, CURLINFO_HTTP_CODE);
curl_close($curl1);

echo "Status Code: $httpCode1\n";
echo "Response: $response1\n";
echo str_repeat("-", 50) . "\n";

echo "\n2. Test avec Content-Type: application/json (CORRECT)...\n";

$curl2 = curl_init();
curl_setopt_array($curl2, [
    CURLOPT_URL => $baseUrl . $endpoint,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',  // CORRECT
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ],
]);

$response2 = curl_exec($curl2);
$httpCode2 = curl_getinfo($curl2, CURLINFO_HTTP_CODE);
curl_close($curl2);

echo "Status Code: $httpCode2\n";
echo "Response: $response2\n";

// Analyser la réponse
$responseData = json_decode($response2, true);

if ($responseData && isset($responseData['success'])) {
    if ($responseData['success']) {
        echo "\n✅ SUCCÈS!\n";
        echo "Dans la zone: " . ($responseData['within_range'] ? 'OUI' : 'NON') . "\n";
        echo "Distance: " . ($responseData['distance'] ?? 'N/A') . "m\n";
        echo "Site: " . ($responseData['site'] ?? 'N/A') . "\n";
    } else {
        echo "\n❌ Erreur API: " . ($responseData['message'] ?? 'Erreur inconnue') . "\n";
    }
} else {
    echo "\n❌ Réponse invalide ou erreur serveur\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "CONCLUSION\n";
echo str_repeat("=", 60) . "\n";
echo "Le Content-Type doit être 'application/json' pour que Laravel\n";
echo "puisse parser correctement les données JSON dans la requête.\n";
