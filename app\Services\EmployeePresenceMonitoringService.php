<?php

namespace App\Services;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log as LaravelLog;

/**
 * Service de surveillance de présence des employés sur leurs chantiers assignés
 */
class EmployeePresenceMonitoringService
{
    private LocationService $locationService;
    
    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    /**
     * Vérifie si un employé est présent sur son chantier assigné
     */
    public function checkEmployeeOnAssignedSite(int $userId, float $latitude, float $longitude): array
    {
        try {
            $user = User::findOrFail($userId);
            
            // Récupérer le site assigné à l'employé
            $assignedSite = $this->getAssignedSite($userId);
            
            if (!$assignedSite) {
                return [
                    'success' => false,
                    'message' => "Aucun chantier assigné à l'employé {$user->name}",
                    'message_ar' => "لا يوجد موقع عمل مخصص للموظف {$user->name}",
                    'employee' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email
                    ],
                    'is_on_assigned_site' => false,
                    'assigned_site' => null
                ];
            }

            // Calculer la distance entre l'employé et son site assigné
            $distance = $this->locationService->calculateDistance(
                (float) $assignedSite->latitude,
                (float) $assignedSite->longitude,
                $latitude,
                $longitude
            );

            $maxDistance = config('clockin.max_distance', 50);
            $isOnSite = $distance <= $maxDistance;

            // Déterminer le statut de présence
            $presenceStatus = $this->determinePresenceStatus($distance, $maxDistance);
            
            // Enregistrer la vérification
            $this->logPresenceCheck($user, $assignedSite, $latitude, $longitude, $distance, $isOnSite);

            $result = [
                'success' => true,
                'employee' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role
                ],
                'assigned_site' => [
                    'id' => $assignedSite->id,
                    'name' => $assignedSite->name,
                    'latitude' => $assignedSite->latitude,
                    'longitude' => $assignedSite->longitude
                ],
                'current_position' => [
                    'latitude' => $latitude,
                    'longitude' => $longitude
                ],
                'is_on_assigned_site' => $isOnSite,
                'distance_from_site' => round($distance, 2),
                'max_allowed_distance' => $maxDistance,
                'presence_status' => $presenceStatus,
                'check_time' => now()->toISOString(),
                'message' => $this->getPresenceMessage($user->name, $assignedSite->name, $presenceStatus, $distance),
                'message_ar' => $this->getPresenceMessageAr($user->name, $assignedSite->name, $presenceStatus, $distance)
            ];

            // Envoyer notification si l'employé n'est pas sur site
            if (!$isOnSite) {
                $this->sendAbsenceNotification($result);
            }

            return $result;

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la vérification de présence sur site assigné', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'coordinates' => [$latitude, $longitude]
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors de la vérification de présence',
                'message_ar' => 'خطأ أثناء التحقق من الحضور',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Vérifie tous les employés qui ont un pointage actif
     */
    public function checkAllActiveEmployeesOnSites(): array
    {
        $results = [];
        $summary = [
            'total_checked' => 0,
            'present_on_site' => 0,
            'absent_from_site' => 0,
            'no_assigned_site' => 0,
            'notifications_sent' => 0
        ];

        try {
            // Récupérer tous les pointages actifs (non terminés)
            $activePointages = Pointage::whereNull('fin_pointage')
                ->with(['user', 'site'])
                ->get();

            foreach ($activePointages as $pointage) {
                $summary['total_checked']++;
                
                // Utiliser la dernière position connue ou la position de début
                $latitude = $pointage->debut_latitude;
                $longitude = $pointage->debut_longitude;

                $checkResult = $this->checkEmployeeOnAssignedSite(
                    $pointage->user_id,
                    $latitude,
                    $longitude
                );

                if ($checkResult['success']) {
                    if (!isset($checkResult['assigned_site'])) {
                        $summary['no_assigned_site']++;
                    } elseif ($checkResult['is_on_assigned_site']) {
                        $summary['present_on_site']++;
                    } else {
                        $summary['absent_from_site']++;
                        $summary['notifications_sent']++;
                    }
                }

                $results[] = [
                    'pointage_id' => $pointage->id,
                    'started_at' => $pointage->debut_pointage,
                    'check_result' => $checkResult
                ];
            }

            return [
                'success' => true,
                'summary' => $summary,
                'detailed_results' => $results,
                'check_time' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la vérification globale de présence', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors de la vérification globale',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Surveille un employé spécifique en continu
     */
    public function startEmployeeMonitoring(int $userId, int $intervalMinutes = 15): array
    {
        try {
            $user = User::findOrFail($userId);
            
            // Vérifier si l'employé a un pointage actif
            $activePointage = Pointage::where('user_id', $userId)
                ->whereNull('fin_pointage')
                ->first();

            if (!$activePointage) {
                return [
                    'success' => false,
                    'message' => "L'employé {$user->name} n'a pas de pointage actif",
                    'message_ar' => "الموظف {$user->name} ليس لديه تسجيل حضور نشط"
                ];
            }

            // Marquer l'employé comme surveillé
            Cache::put("monitoring:user:{$userId}", [
                'started_at' => now(),
                'interval_minutes' => $intervalMinutes,
                'pointage_id' => $activePointage->id
            ], now()->addHours(12));

            // Enregistrer le début de surveillance
            Log::create([
                'user_id' => $userId,
                'action' => 'monitoring_started',
                'details' => json_encode([
                    'interval_minutes' => $intervalMinutes,
                    'pointage_id' => $activePointage->id
                ])
            ]);

            return [
                'success' => true,
                'message' => "Surveillance démarrée pour {$user->name}",
                'message_ar' => "تم بدء المراقبة للموظف {$user->name}",
                'monitoring_config' => [
                    'user_id' => $userId,
                    'interval_minutes' => $intervalMinutes,
                    'started_at' => now()->toISOString(),
                    'expires_at' => now()->addHours(12)->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors du démarrage de la surveillance',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Arrête la surveillance d'un employé
     */
    public function stopEmployeeMonitoring(int $userId): array
    {
        try {
            $user = User::findOrFail($userId);
            
            $monitoringData = Cache::get("monitoring:user:{$userId}");
            
            if (!$monitoringData) {
                return [
                    'success' => false,
                    'message' => "Aucune surveillance active pour {$user->name}",
                    'message_ar' => "لا توجد مراقبة نشطة للموظف {$user->name}"
                ];
            }

            // Supprimer la surveillance
            Cache::forget("monitoring:user:{$userId}");

            // Enregistrer l'arrêt de surveillance
            Log::create([
                'user_id' => $userId,
                'action' => 'monitoring_stopped',
                'details' => json_encode([
                    'duration_minutes' => now()->diffInMinutes($monitoringData['started_at'])
                ])
            ]);

            return [
                'success' => true,
                'message' => "Surveillance arrêtée pour {$user->name}",
                'message_ar' => "تم إيقاف المراقبة للموظف {$user->name}",
                'monitoring_summary' => [
                    'duration_minutes' => now()->diffInMinutes($monitoringData['started_at']),
                    'stopped_at' => now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'arrêt de la surveillance',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Récupère le site assigné à un employé
     */
    private function getAssignedSite(int $userId)
    {
        return DB::table('assignments')
            ->join('sites', 'assignments.site_id', '=', 'sites.id')
            ->where('assignments.user_id', $userId)
            ->select('sites.*')
            ->first();
    }

    /**
     * Détermine le statut de présence
     */
    private function determinePresenceStatus(float $distance, float $maxDistance): string
    {
        if ($distance <= 10) {
            return 'on_site_exact';
        } elseif ($distance <= $maxDistance) {
            return 'on_site_authorized';
        } elseif ($distance <= 100) {
            return 'nearby_site';
        } elseif ($distance <= 500) {
            return 'far_from_site';
        } else {
            return 'very_far_from_site';
        }
    }

    /**
     * Génère un message de présence en français
     */
    private function getPresenceMessage(string $employeeName, string $siteName, string $status, float $distance): string
    {
        return match($status) {
            'on_site_exact' => "{$employeeName} est présent sur le chantier {$siteName} (position exacte)",
            'on_site_authorized' => "{$employeeName} est présent sur le chantier {$siteName} (à {$distance}m)",
            'nearby_site' => "{$employeeName} est proche du chantier {$siteName} (à {$distance}m)",
            'far_from_site' => "⚠️ {$employeeName} est éloigné du chantier {$siteName} (à {$distance}m)",
            'very_far_from_site' => "🚨 {$employeeName} est très éloigné du chantier {$siteName} (à {$distance}m)",
            default => "Statut de présence inconnu pour {$employeeName}"
        };
    }

    /**
     * Génère un message de présence en arabe
     */
    private function getPresenceMessageAr(string $employeeName, string $siteName, string $status, float $distance): string
    {
        return match($status) {
            'on_site_exact' => "{$employeeName} موجود في موقع العمل {$siteName} (الموقع الدقيق)",
            'on_site_authorized' => "{$employeeName} موجود في موقع العمل {$siteName} (على بعد {$distance}م)",
            'nearby_site' => "{$employeeName} قريب من موقع العمل {$siteName} (على بعد {$distance}م)",
            'far_from_site' => "⚠️ {$employeeName} بعيد عن موقع العمل {$siteName} (على بعد {$distance}م)",
            'very_far_from_site' => "🚨 {$employeeName} بعيد جداً عن موقع العمل {$siteName} (على بعد {$distance}م)",
            default => "حالة الحضور غير معروفة لـ {$employeeName}"
        ];
    }

    /**
     * Enregistre une vérification de présence
     */
    private function logPresenceCheck(User $user, $site, float $latitude, float $longitude, float $distance, bool $isOnSite): void
    {
        DB::table('verifications')->insert([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'date_heure' => now(),
            'is_within_range' => $isOnSite,
            'distance' => $distance,
            'is_automatic' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Log dans le système
        Log::create([
            'user_id' => $user->id,
            'action' => 'presence_check',
            'details' => json_encode([
                'site_name' => $site->name,
                'distance' => $distance,
                'is_on_site' => $isOnSite,
                'coordinates' => [$latitude, $longitude]
            ])
        ]);
    }

    /**
     * Envoie une notification d'absence
     */
    private function sendAbsenceNotification(array $checkResult): void
    {
        try {
            $employee = $checkResult['employee'];
            $site = $checkResult['assigned_site'];
            
            // Éviter le spam de notifications
            $cacheKey = "absence_notification:{$employee['id']}";
            if (Cache::has($cacheKey)) {
                return;
            }

            // Marquer comme notifié pour 30 minutes
            Cache::put($cacheKey, true, now()->addMinutes(30));

            $notificationData = [
                'type' => 'employee_absence',
                'employee' => $employee,
                'site' => $site,
                'distance' => $checkResult['distance_from_site'],
                'status' => $checkResult['presence_status'],
                'check_time' => $checkResult['check_time'],
                'message' => $checkResult['message']
            ];

            // Enregistrer la notification
            Log::create([
                'user_id' => $employee['id'],
                'action' => 'absence_notification',
                'details' => json_encode($notificationData)
            ]);

            // Log pour les administrateurs
            LaravelLog::channel('alerts')->warning('Employé absent du chantier assigné', $notificationData);

            // Ici vous pouvez ajouter l'envoi d'email, SMS, etc.
            // $this->sendEmailNotification($notificationData);
            // $this->sendSmsNotification($notificationData);

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de l\'envoi de notification d\'absence', [
                'employee_id' => $checkResult['employee']['id'],
                'error' => $e->getMessage()
            ]);
        }
    }
}
