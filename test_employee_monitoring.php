<?php

// Script de test pour la surveillance des employés sur leurs chantiers assignés
echo "=== Test de Surveillance des Employés sur Chantiers Assignés ===\n";

// Configuration
$baseUrl = 'http://127.0.0.1:8000';
$loginEndpoint = '/api/auth/login';

// Identifiants admin
$credentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $context = [
        'http' => [
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'timeout' => 30
        ]
    ];
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        $context['http']['content'] = json_encode($data);
    }
    
    $response = file_get_contents($url, false, stream_context_create($context));
    
    if ($response === false) {
        throw new Exception("Erreur lors de la requête vers $url");
    }
    
    return json_decode($response, true);
}

try {
    echo "\n1. Connexion en tant qu'admin...\n";
    
    $loginResponse = makeRequest(
        $baseUrl . $loginEndpoint,
        'POST',
        $credentials,
        [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    );
    
    if (!isset($loginResponse['success']) || !$loginResponse['success']) {
        throw new Exception("Échec de la connexion: " . ($loginResponse['message'] ?? 'Erreur inconnue'));
    }
    
    $token = $loginResponse['data']['token'];
    $user = $loginResponse['data']['user'];
    
    echo "✅ Connexion réussie en tant que {$user['name']}\n";
    
    $authHeaders = [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ];

    echo "\n2. Test de vérification d'employé sur chantier assigné (position correcte)...\n";
    
    $checkData = [
        'user_id' => 2, // Ahmed Benali
        'latitude' => 33.5731, // Position du site principal
        'longitude' => -7.5898
    ];
    
    try {
        $checkResponse = makeRequest(
            $baseUrl . '/api/monitoring/check-employee-on-site',
            'POST',
            $checkData,
            $authHeaders
        );
        
        if ($checkResponse['success']) {
            echo "✅ Vérification réussie\n";
            $data = $checkResponse['data'];
            echo "   Employé: {$data['employee']['name']}\n";
            echo "   Chantier assigné: {$data['assigned_site']['name']}\n";
            echo "   Sur le chantier: " . ($data['is_on_assigned_site'] ? 'OUI' : 'NON') . "\n";
            echo "   Distance: {$data['distance_from_site']}m\n";
            echo "   Statut: {$data['presence_status']}\n";
            echo "   Message: {$data['message']}\n";
        } else {
            echo "❌ Erreur: {$checkResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification: " . $e->getMessage() . "\n";
    }

    echo "\n3. Test de vérification d'employé sur chantier assigné (position éloignée)...\n";
    
    $checkDataFar = [
        'user_id' => 2, // Ahmed Benali
        'latitude' => 34.0209, // Rabat (loin du site)
        'longitude' => -6.8416
    ];
    
    try {
        $checkFarResponse = makeRequest(
            $baseUrl . '/api/monitoring/check-employee-on-site',
            'POST',
            $checkDataFar,
            $authHeaders
        );
        
        if ($checkFarResponse['success']) {
            echo "✅ Vérification réussie (position éloignée)\n";
            $data = $checkFarResponse['data'];
            echo "   Employé: {$data['employee']['name']}\n";
            echo "   Chantier assigné: {$data['assigned_site']['name']}\n";
            echo "   Sur le chantier: " . ($data['is_on_assigned_site'] ? 'OUI' : 'NON') . "\n";
            echo "   Distance: {$data['distance_from_site']}m\n";
            echo "   Statut: {$data['presence_status']}\n";
            echo "   Message: {$data['message']}\n";
            echo "   🚨 NOTIFICATION D'ABSENCE ENVOYÉE\n";
        } else {
            echo "❌ Erreur: {$checkFarResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification éloignée: " . $e->getMessage() . "\n";
    }

    echo "\n4. Test de vérification de tous les employés actifs...\n";
    
    try {
        $allActiveResponse = makeRequest(
            $baseUrl . '/api/monitoring/check-all-active',
            'POST',
            [],
            $authHeaders
        );
        
        if ($allActiveResponse['success']) {
            echo "✅ Vérification globale réussie\n";
            $data = $allActiveResponse['data'];
            $summary = $data['summary'];
            
            echo "   Total vérifié: {$summary['total_checked']}\n";
            echo "   Présents sur chantier: {$summary['present_on_site']}\n";
            echo "   Absents du chantier: {$summary['absent_from_site']}\n";
            echo "   Sans chantier assigné: {$summary['no_assigned_site']}\n";
            echo "   Notifications envoyées: {$summary['notifications_sent']}\n";
            
            if (!empty($data['detailed_results'])) {
                echo "\n   Détails par employé:\n";
                foreach ($data['detailed_results'] as $result) {
                    $checkResult = $result['check_result'];
                    if ($checkResult['success']) {
                        $employee = $checkResult['employee']['name'];
                        $status = $checkResult['is_on_assigned_site'] ? 'PRÉSENT' : 'ABSENT';
                        $distance = $checkResult['distance_from_site'] ?? 'N/A';
                        echo "     - {$employee}: {$status} (Distance: {$distance}m)\n";
                    }
                }
            }
        } else {
            echo "❌ Erreur vérification globale: {$allActiveResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la vérification globale: " . $e->getMessage() . "\n";
    }

    echo "\n5. Test de démarrage de surveillance d'un employé...\n";
    
    $monitoringData = [
        'user_id' => 2, // Ahmed Benali
        'interval_minutes' => 10
    ];
    
    try {
        $startMonitoringResponse = makeRequest(
            $baseUrl . '/api/monitoring/start-monitoring',
            'POST',
            $monitoringData,
            $authHeaders
        );
        
        if ($startMonitoringResponse['success']) {
            echo "✅ Surveillance démarrée\n";
            $data = $startMonitoringResponse['data'];
            echo "   Message: {$data['message']}\n";
            $config = $data['monitoring_config'];
            echo "   Utilisateur ID: {$config['user_id']}\n";
            echo "   Intervalle: {$config['interval_minutes']} minutes\n";
            echo "   Démarré à: {$config['started_at']}\n";
            echo "   Expire à: {$config['expires_at']}\n";
        } else {
            echo "❌ Erreur démarrage surveillance: {$startMonitoringResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors du démarrage de surveillance: " . $e->getMessage() . "\n";
    }

    echo "\n6. Test de récupération du statut de surveillance...\n";
    
    try {
        $statusResponse = makeRequest(
            $baseUrl . '/api/monitoring/status',
            'GET',
            null,
            $authHeaders
        );
        
        if ($statusResponse['success']) {
            echo "✅ Statut de surveillance récupéré\n";
            $data = $statusResponse['data'];
            echo "   Total surveillé: {$data['total_monitored']}\n";
            
            if (!empty($data['active_monitoring'])) {
                echo "   Employés surveillés:\n";
                foreach ($data['active_monitoring'] as $monitoring) {
                    echo "     - {$monitoring['user_name']} (Intervalle: {$monitoring['interval_minutes']}min)\n";
                }
            }
        } else {
            echo "❌ Erreur récupération statut: {$statusResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de la récupération du statut: " . $e->getMessage() . "\n";
    }

    echo "\n7. Test d'arrêt de surveillance...\n";
    
    $stopData = [
        'user_id' => 2 // Ahmed Benali
    ];
    
    try {
        $stopResponse = makeRequest(
            $baseUrl . '/api/monitoring/stop-monitoring',
            'POST',
            $stopData,
            $authHeaders
        );
        
        if ($stopResponse['success']) {
            echo "✅ Surveillance arrêtée\n";
            $data = $stopResponse['data'];
            echo "   Message: {$data['message']}\n";
            $summary = $data['monitoring_summary'];
            echo "   Durée: {$summary['duration_minutes']} minutes\n";
            echo "   Arrêtée à: {$summary['stopped_at']}\n";
        } else {
            echo "❌ Erreur arrêt surveillance: {$stopResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors de l'arrêt de surveillance: " . $e->getMessage() . "\n";
    }

    echo "\n8. Test avec employé sans chantier assigné...\n";
    
    $noSiteData = [
        'user_id' => 1, // Admin (probablement sans assignation)
        'latitude' => 33.5731,
        'longitude' => -7.5898
    ];
    
    try {
        $noSiteResponse = makeRequest(
            $baseUrl . '/api/monitoring/check-employee-on-site',
            'POST',
            $noSiteData,
            $authHeaders
        );
        
        if ($noSiteResponse['success']) {
            echo "✅ Vérification réussie (sans assignation)\n";
            $data = $noSiteResponse['data'];
            echo "   Message: {$data['message']}\n";
        } else {
            echo "⚠️  Résultat attendu: {$noSiteResponse['message']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Erreur lors du test sans assignation: " . $e->getMessage() . "\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur critique: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "FIN DES TESTS DE SURVEILLANCE\n";
echo str_repeat("=", 60) . "\n";

echo "\n📋 RÉSUMÉ DES FONCTIONNALITÉS TESTÉES:\n";
echo "✅ Vérification d'employé sur chantier assigné\n";
echo "✅ Détection d'absence avec notification automatique\n";
echo "✅ Vérification globale de tous les employés actifs\n";
echo "✅ Démarrage de surveillance continue\n";
echo "✅ Récupération du statut de surveillance\n";
echo "✅ Arrêt de surveillance\n";
echo "✅ Gestion des employés sans chantier assigné\n";

echo "\n🎯 ENDPOINTS DE SURVEILLANCE:\n";
echo "POST /api/monitoring/check-employee-on-site - Vérifier employé\n";
echo "POST /api/monitoring/check-all-active - Vérifier tous\n";
echo "POST /api/monitoring/start-monitoring - Démarrer surveillance\n";
echo "POST /api/monitoring/stop-monitoring - Arrêter surveillance\n";
echo "GET /api/monitoring/status - Statut surveillance\n";

echo "\n🔔 TYPES DE NOTIFICATIONS:\n";
echo "• on_site_exact - Position exacte (0-10m)\n";
echo "• on_site_authorized - Sur site autorisé (10-50m)\n";
echo "• nearby_site - Proche du site (50-100m)\n";
echo "• far_from_site - Éloigné du site (100-500m)\n";
echo "• very_far_from_site - Très éloigné (>500m)\n";
