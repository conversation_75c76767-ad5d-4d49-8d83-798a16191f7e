import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/repositories/auth_repository.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_overlay.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _obscurePassword = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthRepository>(
        builder: (context, authRepo, child) {
          return LoadingOverlay(
            isLoading: authRepo.isLoading,
            child: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.spacingL),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.spacingL),
                        child: FormBuilder(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Logo and Title
                              _buildHeader(),
                              
                              const SizedBox(height: AppConstants.spacingXL),
                              
                              // Email Field
                              CustomTextField(
                                name: 'email',
                                label: 'Email',
                                keyboardType: TextInputType.emailAddress,
                                prefixIcon: Icons.email_outlined,
                                validators: [
                                  FormBuilderValidators.required(),
                                  FormBuilderValidators.email(),
                                ],
                              ),
                              
                              const SizedBox(height: AppConstants.spacingM),
                              
                              // Password Field
                              CustomTextField(
                                name: 'password',
                                label: 'Password',
                                obscureText: _obscurePassword,
                                prefixIcon: Icons.lock_outlined,
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                validators: [
                                  FormBuilderValidators.required(),
                                  FormBuilderValidators.minLength(AppConstants.minPasswordLength),
                                ],
                              ),
                              
                              const SizedBox(height: AppConstants.spacingM),
                              
                              // Error Message
                              if (authRepo.errorMessage != null)
                                Container(
                                  padding: const EdgeInsets.all(AppConstants.spacingS),
                                  decoration: BoxDecoration(
                                    color: AppColors.error.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                                    border: Border.all(color: AppColors.error),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.error_outline, color: AppColors.error),
                                      const SizedBox(width: AppConstants.spacingS),
                                      Expanded(
                                        child: Text(
                                          authRepo.errorMessage!,
                                          style: const TextStyle(color: AppColors.error),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              
                              const SizedBox(height: AppConstants.spacingL),
                              
                              // Login Button
                              CustomButton(
                                text: 'Login',
                                onPressed: _handleLogin,
                                isLoading: authRepo.isLoading,
                              ),
                              
                              const SizedBox(height: AppConstants.spacingM),
                              
                              // Forgot Password
                              TextButton(
                                onPressed: () {
                                  // TODO: Navigate to forgot password screen
                                },
                                child: const Text('Forgot Password?'),
                              ),
                              
                              const SizedBox(height: AppConstants.spacingM),
                              
                              // Register Link
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text('Don\'t have an account? '),
                                  TextButton(
                                    onPressed: () {
                                      // TODO: Navigate to register screen
                                    },
                                    child: const Text('Register'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo placeholder
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primaryBlue,
            borderRadius: BorderRadius.circular(40),
          ),
          child: const Icon(
            Icons.access_time,
            size: 40,
            color: AppColors.white,
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingM),
        
        // App Name
        const Text(
          AppConstants.appName,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryBlue,
          ),
        ),
        
        const SizedBox(height: AppConstants.spacingS),
        
        // Subtitle
        Text(
          'Employee Attendance Management',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.grey600,
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      final authRepo = Provider.of<AuthRepository>(context, listen: false);
      
      final success = await authRepo.login(
        formData['email'],
        formData['password'],
      );
      
      if (success && mounted) {
        // Navigate to dashboard
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    }
  }
}
