<?php

// Test de l'API de login
echo "=== Test de l'API de login ClockIn ===\n";

// Configuration
$baseUrl = 'http://127.0.0.1:8000';
$loginEndpoint = '/api/auth/login';

// Données de test
$testCredentials = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

echo "1. Test de l'endpoint: $baseUrl$loginEndpoint\n";

// Préparer la requête
$postData = json_encode($testCredentials);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: ClockIn-Test/1.0'
        ],
        'content' => $postData,
        'timeout' => 30
    ]
]);

try {
    echo "\n2. Envoi de la requête...\n";
    echo "Données: " . $postData . "\n";
    
    $response = file_get_contents($baseUrl . $loginEndpoint, false, $context);
    
    if ($response === false) {
        echo "❌ Erreur: Impossible de contacter le serveur\n";
        echo "Vérifiez que le serveur Laravel fonctionne sur $baseUrl\n";
        echo "Commande: php artisan serve --host=127.0.0.1 --port=8000\n";
        exit(1);
    }
    
    echo "\n3. Réponse reçue:\n";
    echo $response . "\n";
    
    // Analyser la réponse JSON
    $responseData = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "❌ Erreur: Réponse JSON invalide\n";
        echo "Erreur JSON: " . json_last_error_msg() . "\n";
        exit(1);
    }
    
    echo "\n4. Analyse de la réponse:\n";
    
    if (isset($responseData['success']) && $responseData['success'] === true) {
        echo "✓ Login réussi!\n";
        echo "Message: " . ($responseData['message'] ?? 'N/A') . "\n";
        
        if (isset($responseData['data']['token'])) {
            echo "✓ Token reçu: " . substr($responseData['data']['token'], 0, 20) . "...\n";
        }
        
        if (isset($responseData['data']['user'])) {
            echo "✓ Utilisateur: " . ($responseData['data']['user']['name'] ?? 'N/A') . "\n";
        }
        
    } else {
        echo "❌ Login échoué\n";
        echo "Message: " . ($responseData['message'] ?? 'N/A') . "\n";
        
        if (isset($responseData['debug'])) {
            echo "\nDétails de l'erreur:\n";
            echo "Exception: " . ($responseData['debug']['exception'] ?? 'N/A') . "\n";
            echo "Message: " . ($responseData['debug']['message'] ?? 'N/A') . "\n";
            echo "Fichier: " . ($responseData['debug']['file'] ?? 'N/A') . "\n";
            echo "Ligne: " . ($responseData['debug']['line'] ?? 'N/A') . "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n=== Fin du test ===\n";
