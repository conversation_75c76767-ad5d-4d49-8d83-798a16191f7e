<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * Export Excel pour le rapport global des employés
 */
class EmployeeReportExport implements WithMultipleSheets
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Retourne les feuilles du classeur
     */
    public function sheets(): array
    {
        return [
            'Résumé' => new EmployeeSummarySheet($this->data),
            'Détail par Employé' => new EmployeeDetailSheet($this->data),
            'Pointages Complets' => new AllPointagesSheet($this->data)
        ];
    }
}

/**
 * Feuille de résumé
 */
class EmployeeSummarySheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['employees'] as $employeeData) {
            $user = $employeeData['user'];
            $stats = $employeeData['statistics'];
            
            $rows[] = [
                $user->id,
                $user->name,
                $user->email,
                $user->role,
                $stats['total_pointages'],
                $stats['completed_pointages'],
                $stats['total_pointages'] - $stats['completed_pointages'], // En cours
                round($stats['total_hours'], 2),
                $stats['average_daily_hours'],
                count($stats['sites_worked']),
                implode(', ', $stats['sites_worked']),
                $stats['completed_pointages'] > 0 
                    ? round(($stats['completed_pointages'] / $stats['total_pointages']) * 100, 1) . '%'
                    : '0%'
            ];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID Employé',
            'Nom',
            'Email',
            'Rôle',
            'Total Pointages',
            'Pointages Terminés',
            'Pointages En Cours',
            'Total Heures',
            'Moyenne Heures/Jour',
            'Nombre de Sites',
            'Sites Travaillés',
            'Taux de Complétion'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            'A:L' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, 'B' => 25, 'C' => 30, 'D' => 12,
            'E' => 15, 'F' => 18, 'G' => 18, 'H' => 15,
            'I' => 20, 'J' => 15, 'K' => 40, 'L' => 18
        ];
    }

    public function title(): string
    {
        return 'Résumé Employés';
    }
}

/**
 * Feuille de détail par employé
 */
class EmployeeDetailSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['employees'] as $employeeData) {
            $user = $employeeData['user'];
            
            // En-tête employé
            $rows[] = [
                "EMPLOYÉ: {$user->name}",
                '', '', '', '', '', '', '', '', ''
            ];
            
            foreach ($employeeData['pointages'] as $pointage) {
                $duration = '';
                if ($pointage->fin_pointage) {
                    $start = \Carbon\Carbon::parse($pointage->debut_pointage);
                    $end = \Carbon\Carbon::parse($pointage->fin_pointage);
                    $duration = $start->diff($end)->format('%H:%I:%S');
                }
                
                $rows[] = [
                    $pointage->id,
                    $pointage->site->name,
                    $pointage->debut_pointage ? \Carbon\Carbon::parse($pointage->debut_pointage)->format('Y-m-d') : '',
                    $pointage->debut_pointage ? \Carbon\Carbon::parse($pointage->debut_pointage)->format('H:i:s') : '',
                    $pointage->fin_pointage ? \Carbon\Carbon::parse($pointage->fin_pointage)->format('H:i:s') : 'En cours',
                    $duration,
                    $pointage->fin_pointage ? 'Terminé' : 'En cours',
                    round($pointage->debut_latitude, 6),
                    round($pointage->debut_longitude, 6),
                    $pointage->fin_latitude ? round($pointage->fin_latitude, 6) : ''
                ];
            }
            
            // Ligne vide entre employés
            $rows[] = ['', '', '', '', '', '', '', '', '', ''];
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID Pointage',
            'Site',
            'Date',
            'Heure Début',
            'Heure Fin',
            'Durée',
            'Statut',
            'Lat. Début',
            'Lon. Début',
            'Lat. Fin'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47']
                ],
                'font' => ['color' => ['rgb' => 'FFFFFF']]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 12, 'B' => 25, 'C' => 12, 'D' => 12,
            'E' => 12, 'F' => 12, 'G' => 12, 'H' => 15,
            'I' => 15, 'J' => 15
        ];
    }

    public function title(): string
    {
        return 'Détail par Employé';
    }
}

/**
 * Feuille de tous les pointages
 */
class AllPointagesSheet implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];
        
        foreach ($this->data['employees'] as $employeeData) {
            foreach ($employeeData['pointages'] as $pointage) {
                $duration = '';
                if ($pointage->fin_pointage) {
                    $start = \Carbon\Carbon::parse($pointage->debut_pointage);
                    $end = \Carbon\Carbon::parse($pointage->fin_pointage);
                    $duration = $start->diff($end)->format('%H:%I:%S');
                }
                
                $rows[] = [
                    $pointage->id,
                    $employeeData['user']->name,
                    $employeeData['user']->email,
                    $pointage->site->name,
                    $pointage->debut_pointage ? \Carbon\Carbon::parse($pointage->debut_pointage)->format('Y-m-d H:i:s') : '',
                    $pointage->fin_pointage ? \Carbon\Carbon::parse($pointage->fin_pointage)->format('Y-m-d H:i:s') : '',
                    $duration,
                    $pointage->fin_pointage ? 'Terminé' : 'En cours',
                    round($pointage->debut_latitude, 6),
                    round($pointage->debut_longitude, 6),
                    $pointage->fin_latitude ? round($pointage->fin_latitude, 6) : '',
                    $pointage->fin_longitude ? round($pointage->fin_longitude, 6) : ''
                ];
            }
        }
        
        return $rows;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Employé',
            'Email',
            'Site',
            'Début Pointage',
            'Fin Pointage',
            'Durée',
            'Statut',
            'Lat. Début',
            'Lon. Début',
            'Lat. Fin',
            'Lon. Fin'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFC000']
                ]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8, 'B' => 20, 'C' => 25, 'D' => 25,
            'E' => 18, 'F' => 18, 'G' => 12, 'H' => 12,
            'I' => 15, 'J' => 15, 'K' => 15, 'L' => 15
        ];
    }

    public function title(): string
    {
        return 'Tous les Pointages';
    }
}
