<?php

namespace App\Services;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log as LaravelLog;

/**
 * Service de gestion des notifications pour le système ClockIn
 */
class NotificationService
{
    private LocationService $locationService;
    
    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    /**
     * Vérifie si un employé est vraiment présent sur le chantier
     */
    public function verifyEmployeePresence(User $user, float $latitude, float $longitude): array
    {
        try {
            // Récupérer le site assigné
            $assignment = DB::table('assignments')
                ->join('sites', 'assignments.site_id', '=', 'sites.id')
                ->where('assignments.user_id', $user->id)
                ->select('sites.*')
                ->first();

            if (!$assignment) {
                return [
                    'success' => false,
                    'message' => 'Aucun site assigné à cet employé',
                    'is_present' => false
                ];
            }

            // Calculer la distance
            $distance = $this->locationService->calculateDistance(
                (float) $assignment->latitude,
                (float) $assignment->longitude,
                $latitude,
                $longitude
            );

            $isPresent = $distance <= config('clockin.max_distance', 50);
            $maxDistance = config('clockin.max_distance', 50);

            // Enregistrer la vérification
            $this->logPresenceVerification($user, $assignment, $latitude, $longitude, $distance, $isPresent);

            // Déterminer le statut de présence
            $status = $this->determinePresenceStatus($user, $isPresent, $distance);

            return [
                'success' => true,
                'is_present' => $isPresent,
                'distance' => round($distance, 2),
                'max_distance' => $maxDistance,
                'site' => [
                    'id' => $assignment->id,
                    'name' => $assignment->name,
                    'latitude' => $assignment->latitude,
                    'longitude' => $assignment->longitude
                ],
                'status' => $status,
                'verification_time' => now()->toISOString(),
                'message' => $this->getPresenceMessage($status, $distance)
            ];

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la vérification de présence', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'coordinates' => [$latitude, $longitude]
            ]);

            return [
                'success' => false,
                'message' => 'Erreur lors de la vérification',
                'is_present' => false
            ];
        }
    }

    /**
     * Envoie une notification d'alerte si l'employé n'est pas sur site
     */
    public function sendPresenceAlert(User $user, array $verificationResult): bool
    {
        try {
            if ($verificationResult['is_present']) {
                return true; // Pas besoin d'alerte si présent
            }

            $alertData = [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'site_name' => $verificationResult['site']['name'] ?? 'Site inconnu',
                'distance' => $verificationResult['distance'],
                'max_distance' => $verificationResult['max_distance'],
                'verification_time' => $verificationResult['verification_time'],
                'status' => $verificationResult['status']
            ];

            // Envoyer notification par email aux admins
            $this->sendEmailAlert($alertData);

            // Enregistrer l'alerte dans les logs
            Log::create([
                'user_id' => $user->id,
                'action' => 'presence_alert',
                'details' => json_encode($alertData)
            ]);

            // Mettre en cache pour éviter le spam
            Cache::put("presence_alert:{$user->id}", true, now()->addMinutes(30));

            return true;

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de l\'envoi d\'alerte de présence', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Vérifie tous les employés qui devraient être en pointage
     */
    public function checkAllActiveEmployees(): array
    {
        $results = [];
        
        // Récupérer tous les pointages actifs (non terminés)
        $activePointages = Pointage::active()
            ->with(['user', 'site'])
            ->get();

        foreach ($activePointages as $pointage) {
            // Pour chaque pointage actif, on pourrait demander une vérification de position
            // Ici, on simule une vérification basée sur la dernière position connue
            
            $lastVerification = $this->getLastVerification($pointage->user_id);
            
            if ($lastVerification) {
                $verificationResult = $this->verifyEmployeePresence(
                    $pointage->user,
                    $lastVerification->latitude,
                    $lastVerification->longitude
                );

                $results[] = [
                    'pointage_id' => $pointage->id,
                    'user' => $pointage->user->name,
                    'site' => $pointage->site->name,
                    'started_at' => $pointage->debut_pointage,
                    'verification' => $verificationResult
                ];

                // Envoyer alerte si nécessaire
                if (!$verificationResult['is_present']) {
                    $this->sendPresenceAlert($pointage->user, $verificationResult);
                }
            }
        }

        return $results;
    }

    /**
     * Génère un rapport de présence pour une période donnée
     */
    public function generatePresenceReport(Carbon $startDate, Carbon $endDate, ?int $userId = null): array
    {
        $query = DB::table('verifications')
            ->join('users', 'verifications.user_id', '=', 'users.id')
            ->join('sites', 'verifications.site_id', '=', 'sites.id')
            ->whereBetween('verifications.date_heure', [$startDate, $endDate])
            ->select(
                'verifications.*',
                'users.name as user_name',
                'users.email as user_email',
                'sites.name as site_name'
            );

        if ($userId) {
            $query->where('verifications.user_id', $userId);
        }

        $verifications = $query->orderBy('verifications.date_heure', 'desc')->get();

        $stats = [
            'total_verifications' => $verifications->count(),
            'present_count' => $verifications->where('is_within_range', true)->count(),
            'absent_count' => $verifications->where('is_within_range', false)->count(),
            'users_checked' => $verifications->pluck('user_id')->unique()->count()
        ];

        return [
            'period' => [
                'start' => $startDate->toISOString(),
                'end' => $endDate->toISOString()
            ],
            'statistics' => $stats,
            'verifications' => $verifications->toArray()
        ];
    }

    /**
     * Enregistre une vérification de présence
     */
    private function logPresenceVerification(User $user, $site, float $latitude, float $longitude, float $distance, bool $isPresent): void
    {
        DB::table('verifications')->insert([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'date_heure' => now(),
            'is_within_range' => $isPresent,
            'distance' => $distance,
            'is_automatic' => false,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Détermine le statut de présence
     */
    private function determinePresenceStatus(User $user, bool $isPresent, float $distance): string
    {
        if ($isPresent) {
            return $distance <= 10 ? 'on_site_exact' : 'on_site_nearby';
        }

        if ($distance <= 100) {
            return 'nearby_site';
        } elseif ($distance <= 500) {
            return 'far_from_site';
        } else {
            return 'very_far_from_site';
        }
    }

    /**
     * Génère un message de présence
     */
    private function getPresenceMessage(string $status, float $distance): string
    {
        return match($status) {
            'on_site_exact' => 'Employé présent sur site (position exacte)',
            'on_site_nearby' => "Employé présent sur site (à {$distance}m)",
            'nearby_site' => "Employé proche du site (à {$distance}m)",
            'far_from_site' => "Employé éloigné du site (à {$distance}m)",
            'very_far_from_site' => "Employé très éloigné du site (à {$distance}m)",
            default => "Statut de présence inconnu"
        };
    }

    /**
     * Envoie une alerte par email
     */
    private function sendEmailAlert(array $alertData): void
    {
        // Ici vous pouvez implémenter l'envoi d'email
        // Pour l'instant, on log l'alerte
        LaravelLog::channel('alerts')->warning('Alerte de présence', $alertData);
    }

    /**
     * Récupère la dernière vérification d'un utilisateur
     */
    private function getLastVerification(int $userId)
    {
        return DB::table('verifications')
            ->where('user_id', $userId)
            ->orderBy('date_heure', 'desc')
            ->first();
    }
}
